package com.hkspeedup.common.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Switch
import androidx.compose.material.SwitchDefaults
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.service.ConfigService
import com.hkspeedup.common.service.config
import com.hkspeedup.common.viewmodel.ViewModel
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.minimize
import flyshadow.common.generated.resources.minimizeAfterStartUp
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource

@Composable
actual fun MinimizeAfterStartupView(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    var launchOnStartUp by remember { mutableStateOf(config.minimizeAfterStartUp) }
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 10.dp).enableFocusable(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.minimize),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.minimizeAfterStartUp),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
            )
            Switch(modifier = Modifier.enableFocusable(), checked = launchOnStartUp, onCheckedChange = {
                launchOnStartUp = it
                config.minimizeAfterStartUp = it
                ConfigService.save()
            },
                colors = SwitchDefaults.colors(
                    checkedTrackColor = Color(0xFFFFA500),
                    checkedTrackAlpha = 1f,
                    checkedThumbColor = MaterialTheme.colors.surface
                )
            )
        }
    }
}
