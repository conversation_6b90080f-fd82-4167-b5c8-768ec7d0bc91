package com.hkspeedup.common.ui.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogState
import androidx.compose.ui.window.DialogWindow
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.hint
import flyshadow.common.generated.resources.ok
import org.jetbrains.compose.resources.stringResource

@Composable
actual fun InitAlertDialog() {
    var showAlertDialog by remember { mutableStateOf(false) }
    var alertDialogStringContentVo: StringContentVo? by remember { mutableStateOf(null) }
    var alertDialogClose by remember { mutableStateOf({}) }

    ViewFunction.tempAlertDialogMessage?.let {
        ViewFunction.tempAlertDialogMessage = null
        ViewFunction.setWindowVisible(true)
        showAlertDialog = true
        alertDialogStringContentVo = it.content
        alertDialogClose = it.onClose
    }
    ViewFunction.showAlertDialogFun = { alertDialogMessage: AlertDialogMessage, closeCallback: () -> Unit ->
        ViewFunction.tempAlertDialogMessage = null
        ViewFunction.setWindowVisible(true)
        showAlertDialog = true
        alertDialogStringContentVo = alertDialogMessage.content
        alertDialogClose = {
            alertDialogMessage.onClose
            closeCallback()
        }
    }

    if (showAlertDialog) {
        val alertDialogContent = alertDialogStringContentVo?.let { it ->
            (it.stringResource?.let { it1 -> stringResource(it1) } ?: "") +
                    (it.string?.let { ",$it" } ?: "")
        } ?: ""
        DialogWindow(
            alwaysOnTop = true,
            state = DialogState(width = 400.dp, height = 150.dp),
            onCloseRequest = {
                showAlertDialog = false
                alertDialogClose()
            },
            title = stringResource(Res.string.hint)
        ) {
            Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier.fillMaxSize().background(MaterialTheme.colors.surface)) {
                Text(
                    alertDialogContent,
                    color = MaterialTheme.colors.onSurface
                )
                Button(onClick = {
                    showAlertDialog = false
                    alertDialogClose()
                }, Modifier.padding(top = 10.dp)) {
                    Text(stringResource(Res.string.ok))
                }
            }
        }
    }
}
