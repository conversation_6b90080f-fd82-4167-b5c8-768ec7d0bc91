package com.hkspeedup.common.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.hutool.core.util.StrUtil
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.ui.component.BackNavBar
import com.hkspeedup.common.util.*
import com.hkspeedup.common.viewmodel.ViewModel
import flyshadow.common.generated.resources.*
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.clickToCancel
import flyshadow.common.generated.resources.clickToDownload
import flyshadow.common.generated.resources.version
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource


@Composable
actual fun OpenAppFolder() {
    Divider(Modifier.padding(start = 10.dp))
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth()
            .height(35.dp)
            .clip(RoundedCornerShape(10.dp))
            .enableFocusable()
            .clickable {
                openFileDir("${getConfigFilePath()}/.FlyShadow")
            }
    ) {
        Row(
            Modifier.weight(1f).padding(start = 10.dp, end = 20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.open),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.openConfigFolder),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                color = MaterialTheme.colors.onSurface,
                modifier = Modifier.padding(start = 5.dp).weight(1f)
            )
        }
    }
}
