package com.hkspeedup.common.ui

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Window
import androidx.compose.ui.window.WindowPosition
import androidx.compose.ui.window.rememberWindowState
import cn.hutool.system.SystemUtil
import com.hkspeedup.common.Launch_Args
import com.hkspeedup.common.service.config
import com.hkspeedup.common.ui.component.InitAlertDialog
import com.hkspeedup.common.ui.component.InitLoading
import com.hkspeedup.common.ui.theme.lightThemeColor
import com.hkspeedup.common.ui.theme.darkThemeColor
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.viewmodel.ViewModel
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.ic_launcher_round
import org.jetbrains.compose.resources.painterResource


@Composable
actual fun InitMainUiExpect(viewModel: ViewModel) {
    var windowVisible by remember { mutableStateOf(!(Launch_Args.contains("--mini") || config.minimizeAfterStartUp)) }
    var alwaysOnTop by remember { mutableStateOf(!SystemUtil.getOsInfo().isWindows) }

    ViewFunction.setWindowVisible = {
        windowVisible = it ?: !windowVisible
        if (windowVisible && !alwaysOnTop) {
            alwaysOnTop = true
            alwaysOnTop = false
        }
    }

    val rememberWindowState =
        rememberWindowState(width = 400.dp, height = 650.dp, position = WindowPosition.Aligned(Alignment.Center))

    rememberWindowState.size = if (windowVisible) DpSize(width = 400.dp, height = 650.dp) else DpSize.Zero
    rememberWindowState.position = WindowPosition.Aligned(Alignment.Center)

    Window(
        alwaysOnTop = alwaysOnTop,
        title = "FlyShadow",
        visible = windowVisible,
        onCloseRequest = { windowVisible = false },
        resizable = false,
        state = rememberWindowState,
        icon = painterResource(Res.drawable.ic_launcher_round)
    ) {
        MaterialTheme(colors = if (viewModel.darkMode) darkThemeColor else lightThemeColor) {
            // 警告弹窗
            InitAlertDialog()
            // 主要内容
            InitMainContent(viewModel)
            // 加载框
            InitLoading()
        }
    }
}
