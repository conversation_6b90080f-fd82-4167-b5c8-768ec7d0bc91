package com.hkspeedup.common

import androidx.compose.runtime.*
import androidx.compose.ui.window.ApplicationScope
import androidx.compose.ui.window.Tray
import androidx.compose.ui.window.application
import cn.hutool.core.util.StrUtil
import com.hkspeedup.common.lib.FlyShadowCore
import com.hkspeedup.common.service.*
import com.hkspeedup.common.ui.InitMainUi
import com.hkspeedup.common.util.*
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.viewmodel.ViewModel
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.*
import kotlinx.coroutines.*
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import java.awt.Toolkit
import java.awt.datatransfer.Clipboard
import java.awt.datatransfer.StringSelection
import kotlin.concurrent.thread
import kotlin.system.exitProcess


var Launch_Args: Array<String> = arrayOf()

@OptIn(DelicateCoroutinesApi::class)
actual fun initMain(args: Array<String>) = application {
    // 设置编码环境为UTF-8
    System.setProperty("file.encoding", "UTF-8")
    System.setProperty("sun.jnu.encoding", "UTF-8")
    System.setProperty("native.encoding", "UTF-8")
    System.setProperty("log4j2.ConsoleEncoding", "UTF-8")
    System.setProperty("log4j.configurationFile", "log4j2.xml")
    GlobalLog.info("Launch args: ${GsonUtil.createGson().toJson(args)}")
    Launch_Args = args;

    Thread.setDefaultUncaughtExceptionHandler { _, e ->
        GlobalLog.error(e, "Uncaught Exception")
    }

    val viewModel = ViewModel()

    // 加载配置
    ConfigService.load(viewModel)
    // 状态栏图标
    ApplicationTray(viewModel)
    // 主程序ui
    InitMainUi(viewModel)

    // App禁止重复运行
    if (!AppLocker.tryLock()) {
        GlobalLog.error("Another instance is already running.")
        ViewFunction.showAlertDialog(AlertDialogMessage(StringContentVo(Res.string.isAlreadyRunning, null)) {
            GlobalLog.error("exitApplication")
            exitApplication()
        })
    } else {
        val exitApplication = { exitApplication() }
        thread {
            runCatching {
                FlyShadowCore.initRuntime()
                FlyShadowCore.initTunnelContext()
                WinTunService.setViewModel(viewModel)
                MacTunService.setViewModel(viewModel)
                this::class.java.classLoader?.getResource("geoip/Country-only-cn-private.mmdb")?.readBytes()?.let {
                    FlyShadowCore.setGeoIP("CN", it)
                }
            }.onFailure {
                GlobalLog.error(it, "Load FlyShadowCore Error")
                ViewFunction.showAlertDialog(AlertDialogMessage(StringContentVo(Res.string.loadCoreFail, it.message))) {
                    exitApplication()
                    exitProcess(0)
                }
            }.onSuccess {
                ConfigService.loadDomainRuleList()
                ConfigService.loadLaunchOnStartup()
                Runtime.getRuntime().addShutdownHook(Thread {
                    SystemProxyUtil.disableProxy()
                    ProxyService.stopProxy()
                    ProxyService.stopTunnel {}
                    // 在应用程序退出时，释放文件锁
                    AppLocker.release()
                })
                SystemProxyUtil.disableProxy()
                if (StrUtil.isNotBlank(config.subscribePassword)) {
                    SubscribeService.setSubscribeUrl(config.subscribePassword)
                    GlobalScope.launch(Dispatchers.IO) {
                        PortForwardingService.init(config.subscribePassword)
                    }
                }
                // 启动http代理
                ProxyService.startProxy {
                    it?.let {
                        ViewFunction.showAlertDialog(AlertDialogMessage(StringContentVo(null, it)))
                    }
                }
            }
        }
    }
}

// 状态栏图标
@OptIn(DelicateCoroutinesApi::class)
@Composable
private fun ApplicationScope.ApplicationTray(viewModel: ViewModel) {
    viewModel.language
    val iconPath = when (viewModel.proxyStatus) {
        ProxyStatus.START -> Res.drawable.ic_launcher_round
        else -> Res.drawable.ic_launcher_round_gray
    }
    var selectedTabIndex by remember {
        mutableStateOf(config.proxyType)
    }
    DisposableEffect(Unit) {
        val job = GlobalScope.launch(Dispatchers.IO) {
            while (true) {
                withContext(Dispatchers.Main) {
                    selectedTabIndex = config.proxyType
                }
                delay(1000)
            }
        }
        onDispose {
            job.cancel() // 在界面消失时取消协程
        }
    }

    Tray(
        icon = painterResource(iconPath),
        menu = {
            CheckboxItem(
                stringResource(Res.string.start),
                checked = viewModel.proxyStatus != ProxyStatus.STOP,
                onCheckedChange = {
                    if (viewModel.proxyStatus != ProxyStatus.STOP) {
                        return@CheckboxItem
                    }
                    ProxyService.startTunnel()
                })
            CheckboxItem(
                stringResource(Res.string.stop),
                checked = viewModel.proxyStatus == ProxyStatus.STOP,
                onCheckedChange = {
                    ProxyService.stopTunnel {}
                })
            Separator()
            CheckboxItem(stringResource(Res.string.direct), checked = selectedTabIndex == 0, onCheckedChange = {
                FlyShadowCore.setProxyType(0)
                config.proxyType = 0
                selectedTabIndex = 0
                ConfigService.save()
            })
            CheckboxItem(stringResource(Res.string.rule), checked = selectedTabIndex == 2, onCheckedChange = {
                FlyShadowCore.setProxyType(3)
                config.proxyType = 2
                selectedTabIndex = 2
                ConfigService.save()
            })
            CheckboxItem(stringResource(Res.string.global), checked = selectedTabIndex == 1, onCheckedChange = {
                FlyShadowCore.setProxyType(2)
                config.proxyType = 1
                selectedTabIndex = 1
                ConfigService.save()
            })
            Separator()
            if (FlyShadowCore.initFinish() && (SystemUtil.isWindows() || SystemUtil.isMac())) {
                CheckboxItem(
                    "${stringResource(Res.string.tunMode)} ${stringResource(Res.string.start)}",
                    checked = viewModel.tunEnable,
                    enabled = !viewModel.tunOpening,
                    onCheckedChange = {
                        if (viewModel.tunEnable) {
                            return@CheckboxItem
                        }
                        config.tunModelEnable = true
                        viewModel.tunEnable = true
                        ConfigService.save()
                        GlobalScope.launch(Dispatchers.IO) {
                            try {
                                if (config.tunModelEnable && viewModel.proxyStatus != ProxyStatus.STOP) {
                                    TunService.openTun()
                                } else {
                                    TunService.stopTun()
                                }
                            } catch (e: Exception) {
                                GlobalLog.error(e, "Open win tun error")
                                ViewFunction.showAlertDialog(
                                    AlertDialogMessage(
                                        StringContentVo(
                                            Res.string.startTunFail,
                                            e.message
                                        )
                                    )
                                )
                                config.tunModelEnable = false
                                viewModel.tunEnable = false
                                ConfigService.save()
                            }
                        }
                    })
                CheckboxItem(
                    "${stringResource(Res.string.tunMode)} ${stringResource(Res.string.stop)}",
                    checked = !viewModel.tunEnable,
                    enabled = !viewModel.tunOpening,
                    onCheckedChange = {
                        if (!viewModel.tunEnable) {
                            return@CheckboxItem
                        }
                        config.tunModelEnable = false
                        viewModel.tunEnable = false
                        ConfigService.save()
                        GlobalScope.launch(Dispatchers.IO) {
                            try {
                                if (config.tunModelEnable && viewModel.proxyStatus != ProxyStatus.STOP) {
                                    TunService.openTun()
                                } else {
                                    TunService.stopTun()
                                }
                            } catch (e: Exception) {
                                GlobalLog.error(e, "Open win tun error")
                                ViewFunction.showAlertDialog(
                                    AlertDialogMessage(
                                        StringContentVo(
                                            Res.string.startTunFail,
                                            e.message
                                        )
                                    )
                                )
                                config.tunModelEnable = true
                                viewModel.tunEnable = true
                                ConfigService.save()
                            }
                        }
                    })
                Separator()
            }
            Item(
                stringResource(Res.string.copyProxySettingsCommand),
                onClick = {
                    runCatching {
                        // 获取系统剪切板
                        val clipboard: Clipboard = Toolkit.getDefaultToolkit().getSystemClipboard()
                        // 创建一个字符串内容的 Transferable 对象
                        val stringSelection = StringSelection(CommandUtil.getProxySettingsCommand())
                        // 将字符串内容设置到剪切板
                        clipboard.setContents(stringSelection, null)
                    }.onFailure {
                        GlobalLog.error(it, "Copy Proxy Command Error")
                    }
                }
            )
            Separator()
            Item(
                stringResource(Res.string.openProgram),
                onClick = { ViewFunction.setWindowVisible(true) }
            )
            Item(
                stringResource(Res.string.exit),
                onClick = {
                    runCatching {
                        thread {
                            ProxyService.stopProxy()
                            ProxyService.stopTunnel {}
                        }.join()
                    }
                    AppLocker.release()
                    exitApplication()
                }
            )
        }, onAction = {
            ViewFunction.setWindowVisible(null)
        })
}
