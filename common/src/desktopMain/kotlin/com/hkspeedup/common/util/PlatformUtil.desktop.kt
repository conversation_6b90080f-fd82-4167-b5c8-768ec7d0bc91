package com.hkspeedup.common.util

import cn.hutool.system.SystemUtil

actual object PlatformUtil {
    actual fun getPlatformId(): Int {
        return if (SystemUtil.getOsInfo().isWindows) {
            3
        } else if (SystemUtil.getOsInfo().isMac) {
            val arch = SystemUtil.getOsInfo().arch
            return if (arch.contains("arm", true) || arch.contains("aarch64", true)) {
                1
            } else {
                2
            }
        } else {
            -1
        }
    }
}