package com.hkspeedup.common.util

import cn.hutool.core.util.RuntimeUtil
import cn.hutool.system.SystemUtil
import com.hkspeedup.common.service.SubscribeService
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.installTips
import flyshadow.common.generated.resources.invalidJsonFile
import java.awt.FileDialog
import java.awt.Frame
import java.io.File

actual fun getConfigFilePath(): String {
    return System.getProperty("user.home")
}

actual fun openProgramInstallFile(filePath: String) {
    ViewFunction.showAlertDialog(
        AlertDialogMessage(
            StringContentVo(
                Res.string.installTips,
                null
            )
        )
    ) {
        if (SystemUtil.getOsInfo().isWindows) {
            val r = RuntimeUtil.execForStr("cmd.exe", "/c", "start", filePath)
            GlobalLog.info("Open Install File result: $r")
        } else if (SystemUtil.getOsInfo().isMac) {
            val r = RuntimeUtil.execForStr("open", filePath)
            GlobalLog.info("Open Install File result: $r")
        }
    }
}

actual fun openFileDir(filePath: String) {
    if (SystemUtil.getOsInfo().isWindows) {
        val r = RuntimeUtil.execForStr("cmd.exe", "/c", "start", filePath)
        GlobalLog.info("Open Install File result: $r")
    } else if (SystemUtil.getOsInfo().isMac) {
        val r = RuntimeUtil.execForStr("open", filePath)
        GlobalLog.info("Open Install File result: $r")
    }
}

actual fun importJsonSubscribeFile() {
    try {
        val fileDialog = FileDialog(null as Frame?, "Select JSON File", FileDialog.LOAD)
        fileDialog.setFilenameFilter { _, name -> name.lowercase().endsWith(".json") }
        fileDialog.isVisible = true

        val selectedFile = fileDialog.file
        val selectedDir = fileDialog.directory

        val absolutePath = File(selectedDir, selectedFile).absolutePath
        if (!absolutePath.endsWith(".json")) {
            ViewFunction.showAlertDialog(
                AlertDialogMessage(
                    StringContentVo(
                        Res.string.invalidJsonFile,
                        ""
                    )
                )
            )
            return
        }
        SubscribeService.importSubscribeFromFile(absolutePath)
    } catch (e: Exception) {
        GlobalLog.error(e, "File selection error")
        null
    }
}