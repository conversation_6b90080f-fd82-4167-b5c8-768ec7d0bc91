package com.hkspeedup.common.util

import java.io.File
import java.nio.channels.FileChannel
import java.nio.channels.FileLock
import java.nio.file.StandardOpenOption


object AppLocker {
    private val lockFile: File = File("${getConfigFilePath()}/.FlyShadow/lock")
    private var lock: FileLock? = null
    private var channel: FileChannel? = null

    fun release() {
        kotlin.runCatching {
            lock?.release()
            channel?.close()
            lockFile.delete()
        }.onSuccess {
            GlobalLog.info("Release App Lock Success")
        }.onFailure {
            GlobalLog.error("Release App Fail", it)
        }
    }

    fun tryLock(): Boolean {
        channel = FileChannel.open(lockFile.toPath(), StandardOpenOption.CREATE, StandardOpenOption.WRITE)
        lock = channel!!.tryLock()
        if (lock == null) {
            channel!!.close()
            return false
        }

        return true
    }
}