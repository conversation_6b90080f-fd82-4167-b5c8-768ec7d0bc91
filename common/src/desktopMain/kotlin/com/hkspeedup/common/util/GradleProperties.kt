package com.hkspeedup.common.util

import java.util.*

actual object GradleProperties {

    private val version: String

    init {
        val systemResourceAsStream = ClassLoader.getSystemResourceAsStream("gradle.properties")
        val properties = Properties()
        properties.load(systemResourceAsStream)
        version = properties.getProperty("app.version")
    }

    actual fun getVersion(): String {
        return version
    }

}