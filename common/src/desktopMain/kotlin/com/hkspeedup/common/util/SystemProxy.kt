package com.hkspeedup.common.util

import cn.hutool.core.util.RuntimeUtil
import cn.hutool.system.SystemUtil
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.unknownOperatingSystem


actual object SystemProxyUtil : ISystemProxy {
    actual override fun enableProxy(host: String, port: Int) {
        val osInfo = SystemUtil.getOsInfo()
        if (osInfo.isWindows) {
            WindowSystemProxyUtil.enableProxy(host, port)
        } else if (osInfo.isMac) {
            MacSystemProxyUtil.enableProxy(host, port)
        } else {
            ViewFunction.setProxyStatus(ProxyStatus.STOP)
            ViewFunction.showAlertDialog(
                AlertDialogMessage(
                    StringContentVo(
                        Res.string.unknownOperatingSystem,
                        osInfo.name
                    )
                )
            )
        }
    }

    actual override fun disableProxy() {
        val osInfo = SystemUtil.getOsInfo()
        if (osInfo.isWindows) {
            WindowSystemProxyUtil.disableProxy()
        } else if (osInfo.isMac) {
            MacSystemProxyUtil.disableProxy()
        } else {
            ViewFunction.setProxyStatus(ProxyStatus.STOP)
            ViewFunction.showAlertDialog(
                AlertDialogMessage(
                    StringContentVo(
                        Res.string.unknownOperatingSystem,
                        osInfo.name
                    )
                )
            )
        }
    }

    actual override fun getProxyConfig(): String {
        val osInfo = SystemUtil.getOsInfo()
        return if (osInfo.isWindows) {
            WindowSystemProxyUtil.getProxyConfig()
        } else if (osInfo.isMac) {
            MacSystemProxyUtil.getProxyConfig()
        } else {
            ""
        }
    }
}

private object WindowSystemProxyUtil : ISystemProxy {
    override fun enableProxy(host: String, port: Int) {
        GlobalLog.info(
            "[CMD]Window Change Proxy Config [$host:$port] result: ${
                kotlin.runCatching {
                    RuntimeUtil.execForStr(
                        "REG", "ADD", "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Internet Settings",
                        "/v", "ProxyServer", "/t", "REG_SZ", "/d", "$host:${port}", "/f"
                    )
                }
            }"
        )
        GlobalLog.info(
            "[CMD]Window Change ProxyOverride result: ${
                runCatching {
                    RuntimeUtil.execForStr(
                        "REG", "ADD", "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Internet Settings",
                        "/v",
                        "ProxyOverride",
                        "/t",
                        "REG_SZ",
                        "/d",
                        "localhost;127.*;<local>;10.*;172.16.*;172.17.*;172.18.*;172.19.*;172.20.*;172.21.*;172.22.*;172.23.*;172.24.*;172.25.*;172.26.*;172.27.*;172.28.*;172.29.*;172.30.*;172.31.*;192.168.*",
                        "/f"
                    )
                }
            }"
        )
        GlobalLog.info(
            "[CMD]Window Enable Proxy result: ${
                runCatching {
                    RuntimeUtil.execForStr(
                        "REG", "ADD", "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Internet Settings",
                        "/v", "ProxyEnable", "/t", "REG_DWORD", "/d", "1", "/f"
                    )
                }
            }"
        )
        GlobalLog.info(
            "[PowerShell]Window Change Proxy Config [$host:$port] result: ${
                runCatching {
                    RuntimeUtil.execForStr(
                        "powershell.exe",
                        "Set-ItemProperty -Path 'HKCU:\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings' -Name ProxyServer -Value '$host:${port}'"
                    )
                }
            }"
        )
        GlobalLog.info(
            "[PowerShell]Window Change ProxyOverride result: ${
                runCatching {
                    RuntimeUtil.execForStr(
                        "powershell.exe",
                        "Set-ItemProperty -Path 'HKCU:\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings' -Name ProxyOverride  -Value 'localhost;127.*;<local>;10.*;172.16.*;172.17.*;172.18.*;172.19.*;172.20.*;172.21.*;172.22.*;172.23.*;172.24.*;172.25.*;172.26.*;172.27.*;172.28.*;172.29.*;172.30.*;172.31.*;192.168.*'"
                    )
                }
            }"
        )
        GlobalLog.info(
            "[PowerShell]Window Enable Proxy result: ${
                runCatching {
                    RuntimeUtil.execForStr(
                        "powershell.exe",
                        "Set-ItemProperty -Path 'HKCU:\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings' -Name ProxyEnable -Value 1"
                    )
                }
            }"
        )
    }

    override fun disableProxy() {
        GlobalLog.info(
            "[CMD]Disable Proxy result: ${
                runCatching {
                    RuntimeUtil.execForStr(
                        "REG", "ADD", "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Internet Settings",
                        "/v", "ProxyEnable", "/t", "REG_DWORD", "/d", "0", "/f"
                    )
                }
            }"
        )
        GlobalLog.info(
            "[PowerShell]Disable Proxy result: ${
                runCatching {
                    RuntimeUtil.execForStr(
                        "powershell.exe",
                        "Set-ItemProperty -Path 'HKCU:\\Software\\Microsoft\\Windows\\CurrentVersion\\Internet Settings' -Name ProxyEnable -Value 0"
                    )
                }
            }"
        )
    }

    override fun getProxyConfig(): String {
        return RuntimeUtil.execForStr(
            "REG", "QUERY", "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Internet Settings",
            "/v", "ProxyServer"
        )
    }

}

private object MacSystemProxyUtil : ISystemProxy {
    override fun enableProxy(host: String, port: Int) {
        getAllNetworkDeviceName()
            .forEach {
                var execForStr = RuntimeUtil.execForStr("networksetup", "-setwebproxy", it, host, port.toString())
                GlobalLog.info("Mac Set WebProxy $it result: $execForStr")
                execForStr = RuntimeUtil.execForStr("networksetup", "-setsecurewebproxy", it, host, port.toString())
                GlobalLog.info("Mac Set SecureWebProxy $it result: $execForStr")
                execForStr = RuntimeUtil.execForStr("networksetup", "-setsocksfirewallproxy", it, host, port.toString())
                GlobalLog.info("Mac Set SocksFirewallProxy $it result: $execForStr")

                execForStr = RuntimeUtil.execForStr("networksetup", "-setwebproxystate", it, "on")
                GlobalLog.info("Mac Enable WebProxy $it result: $execForStr")
                execForStr = RuntimeUtil.execForStr("networksetup", "-setsecurewebproxystate", it, "on")
                GlobalLog.info("Mac Enable SecureWebProxy $it result: $execForStr")
                execForStr = RuntimeUtil.execForStr("networksetup", "-setsocksfirewallproxystate", it, "on")
                GlobalLog.info("Mac Enable SocksFirewallProxy $it result: $execForStr")

            }
    }

    override fun disableProxy() {
        getAllNetworkDeviceName()
            .forEach {
                var execForStr = RuntimeUtil.execForStr("networksetup", "-setwebproxystate", it, "off")
                GlobalLog.info("Mac Disable WebProxy $it result: $execForStr")
                execForStr = RuntimeUtil.execForStr("networksetup", "-setsecurewebproxystate", it, "off")
                GlobalLog.info("Mac Disable SecureWebProxy $it result: $execForStr")
                execForStr = RuntimeUtil.execForStr("networksetup", "-setsocksfirewallproxystate", it, "off")
                GlobalLog.info("Mac Disable SocksFirewallProxy $it result: $execForStr")

            }
    }

    override fun getProxyConfig(): String {
        for (device in getAllNetworkDeviceName()) {
            var execForStr = RuntimeUtil.execForStr("networksetup", "-getwebproxy", device)
            if (execForStr.contains("Enabled: Yes")) {
                val startIndex = execForStr.indexOf("Server: ") + 8
                val host = execForStr.substring(startIndex, execForStr.indexOf("\n", startIndex))
                val startIndex1 = execForStr.indexOf("Port: ") + 6
                val port = execForStr.substring(startIndex1, execForStr.indexOf("\n", startIndex1))
                return "$host:$port"
            }
        }
        return ""
    }

    private fun getAllNetworkDeviceName(): List<String> {
        val execForStr = RuntimeUtil.execForStr("networksetup -listnetworkserviceorder")
        val result = mutableListOf<String>()
        var index = 0
        while (true) {
            val string = "Hardware Port: "
            val startIndex = execForStr.indexOf(string, index)
            index = execForStr.indexOf(",", startIndex)
            if (startIndex == -1) {
                break
            }
            result.add(execForStr.substring(startIndex + string.length, index))
        }
        return result
    }

}