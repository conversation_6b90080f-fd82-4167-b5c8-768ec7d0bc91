package com.hkspeedup.common.util

import cn.hutool.core.lang.caller.CallerUtil
import cn.hutool.log.LogFactory
import cn.hutool.log.StaticLog

actual class GlobalLog {

    actual companion object {
        actual fun info(format: String, vararg arguments: Any?) {
            StaticLog.info(LogFactory.get(CallerUtil.getCaller(3)), format, arguments)
        }

        actual fun debug(format: String, vararg arguments: Any?) {
            StaticLog.debug(LogFactory.get(CallerUtil.getCaller(3)), format, arguments)
        }

        actual fun error(format: String, vararg arguments: Any?) {
            StaticLog.error(LogFactory.get(CallerUtil.getCaller(3)), format, arguments)
        }

        actual fun error(e: Throwable, format: String, vararg arguments: Any?) {
            StaticLog.error(LogFactory.get(CallerUtil.getCaller(3)), e, format, arguments)
        }
    }
}