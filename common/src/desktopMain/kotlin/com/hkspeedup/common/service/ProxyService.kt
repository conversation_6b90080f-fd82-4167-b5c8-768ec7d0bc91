package com.hkspeedup.common.service

import cn.hutool.core.util.StrUtil
import com.google.gson.JsonObject
import com.hkspeedup.common.lib.FlyShadowCore
import com.hkspeedup.common.util.AdminCheckAndRestart
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.util.SystemProxyUtil
import com.hkspeedup.common.util.SystemUtil
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.*
import kotlinx.coroutines.*

actual object ProxyService {

    // 声明一个布尔值的回调函数
    private var loginSuccess: Boolean = false

    private var connectTunnelJob: Job? = null

    // Kotlin 提供的全局协程开放
    @OptIn(DelicateCoroutinesApi::class)
    // 开始代理的函数，使用协程进行异步操作
    actual fun startProxy(callback: (message: String?) -> Unit) {
        GlobalScope.launch {
            // 定义一个变量 i
            var port = config.proxyPort
            var errorMessage: String? = null
            for (i in 0 until 5) {
                port += i
                errorMessage = FlyShadowCore.startProxy(port)
                if (StrUtil.isBlank(errorMessage)) {
                    // 保存配置
                    config.proxyPort = port
                    ConfigService.save()
                    // 回调
                    callback(null)
                    return@launch
                }
                delay(1000)
            }
            callback(errorMessage)
        }

    }

    // 停止代理的函数
    actual fun stopProxy() {
        FlyShadowCore.stopProxy()
    }

    // 测试隧道时延函数
    @OptIn(DelicateCoroutinesApi::class)
    // 完成之后的回调函数
    actual fun testTunnelDelay(finish: (Long, String) -> Unit) {
        // 输出开始的日志
        GlobalLog.info("Ping start")

        config.subscribeInfo?.node?.forEach { node ->
            val jsonObject = JsonObject()
            jsonObject.addProperty("host", node.host)
            jsonObject.addProperty("port", node.port)
            jsonObject.addProperty("password", node.password)
            GlobalScope.launch {
                runCatching {
                    finish(FlyShadowCore.testPingTunnel(jsonObject), node.name!!)
                }.onFailure {
                    finish(-1, node.name!!)
                    GlobalLog.error(it, "${node.name} Ping Error")
                }
                // 输出测试结束的日志
                GlobalLog.info("${node.name} Ping end")
            }
        }
    }

    // 开启隧道函数
    @OptIn(DelicateCoroutinesApi::class)
    actual fun startTunnel() {
        if (config.tunModelEnable && !AdminCheckAndRestart.isAdmin()) {
            ViewFunction.showAlertDialog(AlertDialogMessage(StringContentVo(Res.string.restartOnAdministrator, null))) {
                AdminCheckAndRestart.restartAsAdmin()
            }
            GlobalLog.info("restartAsAdmin")
            return
        }
        connectTunnelJob?.cancel()
        connectTunnelJob = GlobalScope.launch(Dispatchers.IO) {
            try {
                TunService.stopTun()
                // 获取节点信息
                val selectNode = config.getSelectNodeInfo()
                // 如果节点信息为空，提示选择节点
                if (selectNode == null) {
                    GlobalLog.info("selectNode is null， not running")
                    ViewFunction.showAlertDialog(
                        AlertDialogMessage(
                            StringContentVo(
                                Res.string.pleaseSelectServer,
                                null
                            )
                        )
                    )
                    ViewFunction.setProxyStatus(ProxyStatus.STOP)
                } else {
                    // 如果节点信息存在，开始连接
                    ViewFunction.setProxyStatus(ProxyStatus.CONNECTING)

                    // 设置原生UDP
                    FlyShadowCore.setNativeUdp(config.nativeUdp && selectNode.nativeUdp?.toInt() == 1)
                    // 设置直连优先参数
                    FlyShadowCore.setDirectConnPriority(
                        config.directConnPriority,
                        config.directConnPriorityTimeout
                    )
                    // 内置DNS
                    FlyShadowCore.setUseBuildInDns(config.useBuildInDns)
                    // 启动Fake IP
                    FlyShadowCore.setFakeIp(config.tunModelEnable)

                    // 设置代理类型
                    when (config.proxyType) {
                        0 -> {
                            // 直连模式
                            FlyShadowCore.setProxyType(0)
                        }

                        1 -> {
                            // 全局模式
                            FlyShadowCore.setProxyType(2)
                        }

                        2 -> {
                            // 规则模式
                            FlyShadowCore.setProxyType(3)
                        }

                        else -> {}
                    }

                    // 设置代理规则
                    ConfigService.loadDomainRuleList()
                    // 连接隧道
                    FlyShadowCore.connectTunnel(selectNode.host!!, selectNode.port!!, selectNode.password!!)

                    for (i in 0 until 5) {
                        if (FlyShadowCore.getTunnelStatus() == 0) {
                            loginSuccess = true
                            break
                        }
                        delay(1000)
                    }

                    // 连接不成功
                    if (!loginSuccess) {
                        FlyShadowCore.closeTunnel()
                        ViewFunction.setProxyStatus(ProxyStatus.STOP)
                        ViewFunction.showAlertDialog(AlertDialogMessage(StringContentVo(Res.string.connectFail, null)))
                        return@launch
                    }

                    try {
                        TunService.openTun()
                    } catch (e: Exception) {
                        ViewFunction.showAlertDialog(
                            AlertDialogMessage(
                                StringContentVo(
                                    Res.string.startTunFail,
                                    e.message
                                )
                            )
                        )
                        throw e
                    }
                    if (config.autoSetSysProxyOnStartUp) {
                        SystemProxyUtil.enableProxy("127.0.0.1", config.proxyPort)
                    }

                    // 获取隧道状态
                    while (loginSuccess) {
                        if (FlyShadowCore.getTunnelStatus() == 0) {
                            ViewFunction.setProxyStatus(ProxyStatus.START)
                            // 获取隧道流量
                            ViewFunction.setUploadTraffic(FlyShadowCore.getTunnelUpload().toInt())
                            ViewFunction.setDownloadTraffic(FlyShadowCore.getTunnelDownload().toInt())
                        } else {
                            ViewFunction.setProxyStatus(ProxyStatus.CONNECTING)
                        }

                        delay(1000)
                    }
                }
            } catch (e: Exception) {
                GlobalLog.error(e, "Start Tunnel Error")
            } finally {
                stopTunnel{}
            }
        }

    }

    // 停止隧道函数
    @OptIn(DelicateCoroutinesApi::class)
    actual fun stopTunnel(stopFinish: () -> Unit) {
        GlobalLog.info("Stop Tunnel")
        // 先设置等待状态
        ViewFunction.setProxyStatus(ProxyStatus.CONNECTING)

        GlobalScope.launch(Dispatchers.IO) {
            // 停止登录成功的标志
            loginSuccess = false
            connectTunnelJob?.cancel()
            connectTunnelJob = null
            kotlin.runCatching {
                // 重设上传和下载数据
                ViewFunction.setUploadTraffic(0)
                ViewFunction.setDownloadTraffic(0)
                // 停止代理状态
                ViewFunction.setProxyStatus(ProxyStatus.STOP)
            }
            TunService.stopTun()
            // 关闭隧道连接
            FlyShadowCore.closeTunnel()
            // 禁用系统代理
            SystemProxyUtil.disableProxy()
            stopFinish()
        }
    }

    // 重新选择隧道连接
    actual fun reSelectTunnel() {
        GlobalLog.info("Reselect Tunnel")
        // 获取节点信息
        val selectNode = config.getSelectNodeInfo()
        // 如果节点信息为空，提示选择节点
        if (selectNode == null) {
            stopTunnel {}
            ViewFunction.showAlertDialog(
                AlertDialogMessage(
                    StringContentVo(
                        Res.string.pleaseSelectServer,
                        null
                    )
                )
            )
        } else if (connectTunnelJob != null) {
            FlyShadowCore.closeTunnel()
            GlobalScope.launch(Dispatchers.IO) {
                // 设置原生UDP
                FlyShadowCore.setNativeUdp(config.nativeUdp && selectNode.nativeUdp?.toInt() == 1)
                // 连接
                FlyShadowCore.connectTunnel(selectNode.host!!, selectNode.port!!, selectNode.password!!)
            }
        }
    }

    actual fun restartTunnel() {

    }

}