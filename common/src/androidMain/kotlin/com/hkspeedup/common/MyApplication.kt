package com.hkspeedup.common

import android.annotation.SuppressLint
import android.app.Application
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.widget.Toast
import android.widget.Toast.LENGTH_LONG
import com.hkspeedup.common.lib.FlyShadowCore
import com.hkspeedup.common.service.*
import com.hkspeedup.common.service.ProxyService.startProxy
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.viewmodel.ViewModel
import com.hkspeedup.common.vo.StringContentVo
import kotlinx.coroutines.*
import java.io.File
import java.io.FileWriter
import java.io.IOException
import java.io.PrintWriter
import kotlin.system.exitProcess


// MyApplication是自定义的Application类，用于在应用程序启动时进行一些初始化操作
class MyApplication : Application() {

    companion object {
        @SuppressLint("StaticFieldLeak")
        var context: Context? = null
        val viewModel = ViewModel()

        var PROXY_STATUS = ProxyStatus.STOP
            set(value) {
                field = value
            }
    }

    private fun saveCrashLog(throwable: Throwable) {
        val logFolder = File(context!!.filesDir, ".FlyShadow")
        if (!logFolder.exists()) {
            logFolder.mkdirs()
        }

        val logFileName = "crash_log.txt"
        val logFilePath = logFolder.absolutePath + File.separator + logFileName

        try {
            val writer = FileWriter(logFilePath, true)
            writer.use {
                PrintWriter(it).use {
                    throwable.printStackTrace(it)
                }
            }

            exitProcess(1)
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    // 在Application的onCreate方法中进行初始化
    @OptIn(DelicateCoroutinesApi::class)
    override fun onCreate() {
        super.onCreate()
        // 将上下文赋值给context变量
        context = this

        // 设置全局的未捕获异常处理程序
        Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
            GlobalLog.error(throwable, "thread :${thread.name}")
            // 保存崩溃日志到外部存储
            saveCrashLog(throwable)
        }

        kotlin.runCatching {
            FlyShadowCore.initRuntime()
            FlyShadowCore.initTunnelContext()
            FlyShadowCore.newTun()
            FlyShadowCore.setFakeIp(true)
            this::class.java.classLoader?.getResource("geoip/Country-only-cn-private.mmdb")?.readBytes()?.let {
                FlyShadowCore.setGeoIP("CN", it)
            }
        }.onFailure {
            GlobalLog.error(it, "Load library Error")
            GlobalScope.launch(Dispatchers.Main) {
                Toast.makeText(context, "Load Core Library Error: $it", LENGTH_LONG).show()
            }
        }


        val intentFilter = IntentFilter()
        intentFilter.addAction(Intent.ACTION_USER_PRESENT)
        intentFilter.addAction(Intent.ACTION_SCREEN_ON)
        registerReceiver(object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                ProxyService.reSelectTunnel()
            }
        }, intentFilter)

        ConfigService.load(viewModel)
        ConfigService.loadDomainRuleList()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            MyTileService.LAUNCH_ON_START_JOB = GlobalScope.launch(Dispatchers.IO) {
                delay(1000)
                ConfigService.loadLaunchOnStartup()
            }

        } else {
            ConfigService.loadLaunchOnStartup()
        }

        if (config.subscribePassword != "") {
            SubscribeService.setSubscribeUrl(config.subscribePassword)
            GlobalScope.launch(Dispatchers.IO) {
                PortForwardingService.init(config.subscribePassword)
            }
        }
        startProxy {
            it?.let {
                ViewFunction.showAlertDialog(AlertDialogMessage(StringContentVo(null, it)))
            }
        }
    }

}
