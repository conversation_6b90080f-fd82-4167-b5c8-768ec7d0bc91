package com.hkspeedup.common.ui

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import com.hkspeedup.common.ui.component.InitAlertDialog
import com.hkspeedup.common.ui.component.InitLoading
import com.hkspeedup.common.ui.theme.lightThemeColor
import com.hkspeedup.common.ui.theme.darkThemeColor
import com.hkspeedup.common.viewmodel.ViewModel

@Composable
actual fun InitMainUiExpect(viewModel: ViewModel) {
    MaterialTheme(colors = if (viewModel.darkMode) darkThemeColor else lightThemeColor) {
        InitAlertDialog()
        InitMainContent(viewModel)
        InitLoading()
    }
}
