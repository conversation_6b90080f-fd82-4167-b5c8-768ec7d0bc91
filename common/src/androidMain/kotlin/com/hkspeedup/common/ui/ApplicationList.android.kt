package com.hkspeedup.common.ui

import android.content.pm.PackageManager
import android.graphics.drawable.Drawable
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.drawable.toBitmap
import com.hkspeedup.common.MyApplication
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.service.ConfigService
import com.hkspeedup.common.service.ProxyService
import com.hkspeedup.common.service.config
import com.hkspeedup.common.ui.component.BackNavBar
import com.hkspeedup.common.ui.component.MyVerticalScrollbar
import com.hkspeedup.common.viewmodel.AppInfoPage
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.viewmodel.ViewModel
import flyshadow.common.generated.resources.*
import kotlinx.coroutines.*
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource


@Composable
actual fun ApplicationList(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    Column(
        Modifier.fillMaxWidth().enableFocusable()
            .clickable {
                ViewFunction.pushNavigationStackPage(AppInfoPage())
            }) {
        Box {
            Row(
                Modifier.padding(horizontal = 10.dp).height(35.dp).fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Image(
                    painter = painterResource(Res.drawable.application),
                    contentDescription = stringResource(Res.string.appList), modifier = Modifier.size(20.dp)
                )
                Text(
                    stringResource(Res.string.appList),
                    fontSize = 13.sp,
                    lineHeight = 1.sp,
                    color = MaterialTheme.colors.onSurface,
                    modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
                )
                Image(
                    painter = painterResource(Res.drawable.right),
                    contentDescription = "", modifier = Modifier.size(15.dp),
                    colorFilter = ColorFilter.tint(color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f))
                )
            }

        }
    }
}

private data class AppInfo(var packageName: String, var icon: Drawable, var appName: String)

@OptIn(ExperimentalLayoutApi::class, DelicateCoroutinesApi::class)
@Composable
actual fun AppInfo(viewModel: ViewModel) {
    val appsInfo = remember { mutableStateListOf<AppInfo>() }
    var searchText by remember { mutableStateOf(TextFieldValue()) }
    val apps = remember { config.apps.toMutableStateList() }
    val appList = appsInfo.filter {
        val textSplit = searchText.text.lowercase().split(" ")
        var result = true
        for (s in textSplit) {
            result = result && it.appName.lowercase().contains(s) ||
                    it.packageName.lowercase().contains(s)
        }
        result
    }
    val allChecked = appList.isNotEmpty() && appList.map { it.packageName }.all {
        apps.contains(it)
    }

    val packageManager: PackageManager = MyApplication.context!!.packageManager
    DisposableEffect(Unit) {
        val job = GlobalScope.launch(Dispatchers.IO) {
            ViewFunction.setWindowLoading(true)
            val packages = packageManager.getInstalledPackages(0)
            val tempAppsInfo = arrayListOf<AppInfo>();
            for (packageInfo in packages) {
                val packageName = packageInfo.packageName
                var icon: Drawable? = null
                var appName: String? = null

                try {
                    val appInfo = packageManager.getApplicationInfo(packageName, 0)
                    icon = packageManager.getApplicationIcon(appInfo)
                    appName = packageManager.getApplicationLabel(appInfo).toString()
                } catch (e: PackageManager.NameNotFoundException) {
                    e.printStackTrace()
                }
                if (apps.contains(packageName)) {
                    tempAppsInfo.add(0, AppInfo(packageName, icon!!, appName!!))
                } else {
                    tempAppsInfo.add(AppInfo(packageName, icon!!, appName!!))
                }
            }
            withContext(Dispatchers.Main) {
                appsInfo.addAll(tempAppsInfo)
            }
            config.apps = config.apps.filter {
                appsInfo.find { app -> app.packageName == it } != null
            }.toHashSet()
            ConfigService.save()
            ViewFunction.setWindowLoading(false)
        }
        // 使用 DisposableEffect 来清理资源或取消工作
        onDispose {
            ViewFunction.setWindowLoading(false)
            job.cancel() // 在界面消失时取消协程
        }
    }

    Box(
        Modifier.padding(10.dp)
            .background(MaterialTheme.colors.surface, shape = RoundedCornerShape(10.dp))
    ) {
        val state = rememberLazyListState()

        // 连接列表标题
        Column {
            BackNavBar(stringResource(Res.string.appList), backCallback = {
                if (viewModel.proxyStatus == ProxyStatus.START) {
                    ProxyService.restartTunnel()
                }
                true
            })

            Divider(Modifier.padding(start = 10.dp))

            Row(
                Modifier.fillMaxWidth().padding(start = 20.dp, end = 20.dp),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                var selectedTabIndex by remember { mutableStateOf(config.excludeAppsMode) }
                TabRow(
                    backgroundColor = MaterialTheme.colors.surface,
                    contentColor = MaterialTheme.colors.primary,
                    selectedTabIndex = selectedTabIndex,
                    modifier = Modifier.clip(RoundedCornerShape(5.dp))
                ) {
                    Tab(
                        modifier = Modifier.width(70.dp).height(40.dp).enableFocusable()
                            .align(Alignment.CenterVertically),
                        selected = selectedTabIndex == 0,
                        onClick = {
                            selectedTabIndex = 0
                            config.excludeAppsMode = 0
                            ConfigService.save()
                        }
                    ) {
                        Text(
                            stringResource(Res.string.disallowedApplication),
                            fontSize = 13.sp,
                            lineHeight = 13.sp,
                            textAlign = TextAlign.Center,
                            color = MaterialTheme.colors.onSurface,
                            modifier = Modifier.height(40.dp)
                        )
                    }
                    Tab(
                        modifier = Modifier.width(45.dp).height(40.dp).enableFocusable()
                            .align(Alignment.CenterVertically),
                        selected = selectedTabIndex == 1,
                        onClick = {
                            selectedTabIndex = 1
                            config.excludeAppsMode = 1
                            ConfigService.save()
                        }
                    ) {
                        Text(
                            stringResource(Res.string.allowedApplication),
                            fontSize = 13.sp,
                            lineHeight = 13.sp,
                            textAlign = TextAlign.Center,
                            color = MaterialTheme.colors.onSurface,
                            modifier = Modifier.height(40.dp)
                        )
                    }
                    Tab(
                        modifier = Modifier.width(50.dp).height(40.dp).enableFocusable()
                            .align(Alignment.CenterVertically),
                        selected = selectedTabIndex == 2,
                        onClick = {
                            selectedTabIndex = 2
                            config.excludeAppsMode = 2
                            ConfigService.save()
                        }
                    ) {
                        Text(
                            stringResource(Res.string.allowedAllApplication),
                            fontSize = 13.sp,
                            lineHeight = 13.sp,
                            textAlign = TextAlign.Center,
                            color = MaterialTheme.colors.onSurface,
                            modifier = Modifier.height(40.dp)
                        )
                    }
                }
            }

            Divider(Modifier.padding(start = 10.dp))

            // 搜索
            Row {
                TextField(
                    value = searchText,
                    onValueChange = {
                        searchText = it
                    },
                    modifier = Modifier
                        .weight(1f).enableFocusable(),
                    placeholder = { Text(text = stringResource(Res.string.searchContent)) },
                    singleLine = true,
                    colors = TextFieldDefaults.textFieldColors(
                        backgroundColor = MaterialTheme.colors.surface,
                        textColor = MaterialTheme.colors.onSurface
                    )
                )
                // 全选
                Checkbox(checked = allChecked, onCheckedChange = { it ->
                    if (it) {
                        apps.addAll(appList.map { it.packageName })
                    } else {
                        apps.removeAll(appList.map { it.packageName })
                    }
                    config.apps = apps.toHashSet()
                    ConfigService.save()
                })
            }

            Divider(Modifier.padding(start = 10.dp))


            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 连接列表
                LazyColumn(state = state, modifier = Modifier.padding(start = 5.dp, end = 5.dp)) {
                    items(appList.size) { index ->
                        SelectionContainer {
                            val app = appList[index]
                            Row(Modifier.fillMaxWidth().padding(5.dp).align(Alignment.Center)) {

                                Image(
                                    bitmap = app.icon.toBitmap().asImageBitmap(),
                                    contentDescription = app.appName,
                                    modifier = Modifier.size(30.dp).padding(end = 5.dp)
                                )

                                Column(Modifier.weight(1f)) {
                                    Text(
                                        app.appName,
                                        fontSize = 13.sp,
                                        lineHeight = 13.sp,
                                        color = MaterialTheme.colors.onSurface
                                    )
                                    Spacer(Modifier.height(2.dp))
                                    FlowRow {
                                        Text(
                                            app.packageName,
                                            fontSize = 13.sp,
                                            lineHeight = 13.sp,
                                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                                        )
                                    }
                                }

                                Checkbox(checked = apps.contains(app.packageName), onCheckedChange = {
                                    if (it) {
                                        apps.add(app.packageName)
                                    } else {
                                        apps.remove(app.packageName)
                                    }
                                    config.apps = apps.toHashSet()
                                    ConfigService.save()
                                })

                            }
                            Divider()
                        }

                    }
                }
                MyVerticalScrollbar(
                    modifier = Modifier.align(Alignment.CenterEnd),
                    state = state
                )
            }
        }

    }

}