package com.hkspeedup.common.ui.component

import android.widget.Toast
import androidx.compose.material.AlertDialog
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.sp
import com.hkspeedup.common.MyApplication
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.hint
import flyshadow.common.generated.resources.ok
import org.jetbrains.compose.resources.stringResource

@Composable
actual fun InitAlertDialog() {
    var showAlertDialog by remember { mutableStateOf(false) }
    var alertDialogStringContentVo: StringContentVo? by remember { mutableStateOf(null) }
    var alertDialogOnClose by remember { mutableStateOf({}) }

    ViewFunction.tempAlertDialogMessage?.let {
        ViewFunction.tempAlertDialogMessage = null
        ViewFunction.setWindowVisible(true)
        showAlertDialog = true
        alertDialogStringContentVo = it.content
        alertDialogOnClose = it.onClose
    }
    ViewFunction.showAlertDialogFun = { alertDialogMessage: AlertDialogMessage, closeCallback: () -> Unit ->
        ViewFunction.tempAlertDialogMessage = null
        ViewFunction.setWindowVisible(true)
        showAlertDialog = true
        alertDialogStringContentVo = alertDialogMessage.content
        alertDialogOnClose = {
            alertDialogMessage.onClose
            closeCallback()
        }
    }

    if (showAlertDialog) {
        val alertDialogContent = alertDialogStringContentVo?.let { it ->
            (it.stringResource?.let { it1 -> stringResource(it1) } ?: "") +
                    (it.string?.let { ",$it" } ?: "")
        } ?: ""
        Toast.makeText(MyApplication.context, "FlyShadow: $alertDialogContent", Toast.LENGTH_LONG).show()
        AlertDialog(onDismissRequest = {
            showAlertDialog = false
            alertDialogOnClose()
        },
            title = { Text(stringResource(Res.string.hint), lineHeight = 1.sp, color = MaterialTheme.colors.onSurface) },
            text = {
                Text(alertDialogContent, color = MaterialTheme.colors.onSurface)
            },
            confirmButton = {
                TextButton(onClick = {
                    showAlertDialog = false
                    alertDialogOnClose()
                }) {
                    Text(text = stringResource(Res.string.ok), color = MaterialTheme.colors.onSurface, lineHeight = 1.sp)
                }
            })
    }
}
