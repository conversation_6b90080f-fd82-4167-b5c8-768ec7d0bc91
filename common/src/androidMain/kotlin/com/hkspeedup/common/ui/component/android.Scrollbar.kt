package com.hkspeedup.common.ui.component

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

@Composable
actual fun MyVerticalScrollbar(modifier: Modifier, state: LazyListState) = Unit

@Composable
actual fun MyVerticalScrollbar(modifier: Modifier, state: ScrollState) = Unit