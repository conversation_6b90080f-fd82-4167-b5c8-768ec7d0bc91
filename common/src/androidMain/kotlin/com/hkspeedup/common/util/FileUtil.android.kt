package com.hkspeedup.common.util

import android.content.Intent
import android.os.Build
import android.provider.Settings
import androidx.annotation.RequiresApi
import androidx.core.content.FileProvider
import androidx.core.net.toUri
import com.hkspeedup.common.MyApplication
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.installPermissionRequired
import java.io.File


actual fun getConfigFilePath():String{
    return "${MyApplication.context!!.filesDir}"
}

actual fun openProgramInstallFile(filePath: String) {
    val context = MyApplication.context!!

    // 检查安装权限
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
        if (!context.packageManager.canRequestPackageInstalls()) {
            // 没有安装权限，请求权限
            requestInstallPermission(filePath)
            return
        }
    }

    // 有权限，直接安装
    performInstall(filePath)
}

@RequiresApi(Build.VERSION_CODES.O)
private fun requestInstallPermission(filePath: String) {
    val context = MyApplication.context!!

    // 保存文件路径，权限获取后使用
    InstallPermissionManager.setPendingInstallFile(filePath)

    try {
        val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES)
        intent.data = "package:${context.packageName}".toUri()
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

        // 通过InstallPermissionManager启动权限请求
        InstallPermissionManager.requestInstallPermission(intent)
    } catch (e: Exception) {
        GlobalLog.error(e, "Failed to request install permission")
        // 显示错误提示
        ViewFunction.showAlertDialog(
            AlertDialogMessage(
                StringContentVo(
                    Res.string.installPermissionRequired,
                    "Install permission is required to install APK files"
                )
            )
        )
    }
}

private fun performInstall(filePath: String) {
    val context = MyApplication.context!!
    val intent = Intent()
    intent.action = Intent.ACTION_VIEW
    intent.flags = Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_ACTIVITY_NEW_TASK

    val contentUri = FileProvider.getUriForFile(context, context.packageName + ".fileprovider", File(filePath))
    intent.setDataAndType(contentUri, "application/vnd.android.package-archive")

    if (context.packageManager.queryIntentActivities(intent, 0).isNotEmpty()) {
        context.startActivity(intent)
    } else {
        GlobalLog.error("No activity found to handle APK installation")
        ViewFunction.showAlertDialog(
            AlertDialogMessage(
                StringContentVo(
                    null,
                    "No application found to install APK files"
                )
            )
        )
    }
}

actual fun openFileDir(filePath: String) {
    GlobalLog.info("Open file: $filePath")
    val context = MyApplication.context!!;
    val folderUri = FileProvider.getUriForFile(context, context.getPackageName() + ".fileprovider", File(filePath))
    val intent = Intent(Intent.ACTION_GET_CONTENT)
    intent.setDataAndType(folderUri, "*/*")
    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    context.startActivity(intent)
}

actual fun importJsonSubscribeFile() {
    // Android端使用FileSelector处理文件选择
    GlobalLog.info("selectJsonFile called on Android")
    FileSelector.selectJsonFile()
}