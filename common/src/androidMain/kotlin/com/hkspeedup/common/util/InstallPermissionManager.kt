package com.hkspeedup.common.util

import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import com.hkspeedup.common.MyApplication
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.installPermissionRequired

/**
 * 安装权限管理器
 * 处理Android 8.0+的安装未知应用权限申请
 */
object InstallPermissionManager {

    private var installPermissionLauncher: ActivityResultLauncher<Intent>? = null
    private var pendingInstallFilePath: String? = null

    /**
     * 初始化安装权限管理器
     * 需要在MainActivity的onCreate中调用
     */
    fun initialize(activity: AppCompatActivity) {
        // 初始化安装权限请求
        installPermissionLauncher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            // 延迟检查权限，给用户时间授予权限
            Handler(Looper.getMainLooper()).postDelayed({
                checkPermissionAndInstall()
            }, 1000) // 延迟检查
        }
    }

    /**
     * 检查权限并执行安装
     */
    private fun checkPermissionAndInstall() {
        // 检查权限是否已授予
        if (checkInstallPermission()) {
            // 权限已授予，执行安装
            pendingInstallFilePath?.let { filePath ->
                performInstall(filePath)
                pendingInstallFilePath = null
            }
        } else {
            // 权限仍未授予，再次延迟检查
            Handler(Looper.getMainLooper()).postDelayed({
                checkPermissionFinalAttempt()
            }, 2000) // 再延迟检查
        }
    }

    /**
     * 最终权限检查尝试
     */
    private fun checkPermissionFinalAttempt() {
        if (checkInstallPermission()) {
            // 权限已授予，执行安装
            pendingInstallFilePath?.let { filePath ->
                performInstall(filePath)
                pendingInstallFilePath = null
            }
        } else {
            // 权限被拒绝，显示提示
            ViewFunction.showAlertDialog(
                AlertDialogMessage(
                    StringContentVo(
                        Res.string.installPermissionRequired,
                        "Install permission is required to install APK files. Please enable 'Install unknown apps' in settings."
                    )
                )
            )
            pendingInstallFilePath = null
        }
    }

    /**
     * 检查安装权限
     */
    fun checkInstallPermission(): Boolean {
        val context = MyApplication.context ?: return false

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.packageManager.canRequestPackageInstalls()
        } else {
            // Android 8.0以下默认有权限
            true
        }
    }

    /**
     * 请求安装权限
     */
    fun requestInstallPermission(intent: Intent) {
        installPermissionLauncher?.launch(intent)
    }

    /**
     * 设置待安装的文件路径
     */
    fun setPendingInstallFile(filePath: String) {
        pendingInstallFilePath = filePath
    }

    /**
     * 执行安装
     */
    private fun performInstall(filePath: String) {
        try {
            // 直接执行安装逻辑，避免循环调用
            val context = MyApplication.context!!
            val intent = Intent()
            intent.action = Intent.ACTION_VIEW
            intent.flags = Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_ACTIVITY_NEW_TASK

            val contentUri = androidx.core.content.FileProvider.getUriForFile(
                context,
                context.packageName + ".fileprovider",
                java.io.File(filePath)
            )
            intent.setDataAndType(contentUri, "application/vnd.android.package-archive")

            if (context.packageManager.queryIntentActivities(intent, 0).isNotEmpty()) {
                context.startActivity(intent)
            } else {
                GlobalLog.error("No activity found to handle APK installation")
                ViewFunction.showAlertDialog(
                    AlertDialogMessage(
                        StringContentVo(
                            null,
                            "No application found to install APK files"
                        )
                    )
                )
            }
        } catch (e: Exception) {
            GlobalLog.error(e, "Failed to install APK: $filePath")
            ViewFunction.showAlertDialog(
                AlertDialogMessage(
                    StringContentVo(
                        null,
                        "Failed to install APK: ${e.message}"
                    )
                )
            )
        }
    }
}
