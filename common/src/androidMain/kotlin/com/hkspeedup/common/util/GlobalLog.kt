package com.hkspeedup.common.util

import android.util.Log
import cn.hutool.core.lang.caller.CallerUtil
import cn.hutool.core.util.ClassUtil

actual class GlobalLog {

    actual companion object {
        actual fun info(format: String, vararg arguments: Any?) {
            val caller = CallerUtil.getCaller(3)
            Log.i(ClassUtil.getClassName(caller, true), format)
        }

        actual fun debug(format: String, vararg arguments: Any?) {
            val caller = CallerUtil.getCaller(3)
            Log.d(ClassUtil.getClassName(caller, true), format)
        }

        actual fun error(format: String, vararg arguments: Any?) {
            val caller = CallerUtil.getCaller(3)
            Log.e(ClassUtil.getClassName(caller, true), format)
        }

        actual fun error(e: Throwable, format: String, vararg arguments: Any?) {
            val caller = CallerUtil.getCaller(3)
            Log.e(ClassUtil.getClassName(caller, true), format, e)
        }
    }
}