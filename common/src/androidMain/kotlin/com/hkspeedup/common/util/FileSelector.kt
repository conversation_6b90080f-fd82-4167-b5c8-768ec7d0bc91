package com.hkspeedup.common.util

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.DocumentsContract
import android.provider.Settings
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.hkspeedup.common.MyApplication
import com.hkspeedup.common.service.SubscribeService
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.fileImportError
import flyshadow.common.generated.resources.invalidJsonFile
import java.io.BufferedReader
import java.io.InputStreamReader

object FileSelector {
    private var filePickerLauncher: ActivityResultLauncher<Intent>? = null
    private var permissionLauncher: ActivityResultLauncher<Array<String>>? = null
    private var manageStoragePermissionLauncher: ActivityResultLauncher<Intent>? = null

    private const val REQUEST_CODE_STORAGE_PERMISSION = 1001

    fun initialize(activity: AppCompatActivity) {
        // 初始化文件选择器
        filePickerLauncher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                result.data?.data?.let { uri ->
                    handleSelectedFile(uri)
                }
            }
        }

        // 初始化权限请求
        permissionLauncher = activity.registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            val allGranted = permissions.values.all { it }
            if (allGranted) {
                // 权限已授予，启动文件选择器
                launchFilePicker()
            } else {
                // 权限被拒绝
                ViewFunction.showAlertDialog(
                    AlertDialogMessage(
                        StringContentVo(
                            Res.string.fileImportError,
                            "Storage permission is required to import files"
                        )
                    )
                )
            }
        }

        // Android 11+ 的存储管理权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            manageStoragePermissionLauncher = activity.registerForActivityResult(
                ActivityResultContracts.StartActivityForResult()
            ) { result ->
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    if (android.os.Environment.isExternalStorageManager()) {
                        // 权限已授予，启动文件选择器
                        launchFilePicker()
                    } else {
                        // 权限被拒绝
                        ViewFunction.showAlertDialog(
                            AlertDialogMessage(
                                StringContentVo(
                                    Res.string.fileImportError,
                                    "Storage management permission is required"
                                )
                            )
                        )
                    }
                }
            }
        }
    }

    fun selectJsonFile() {
        if (checkStoragePermission()) {
            launchFilePicker()
        } else {
            requestStoragePermission()
        }
    }

    private fun checkStoragePermission(): Boolean {
        val context = MyApplication.context ?: return false

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 使用 MANAGE_EXTERNAL_STORAGE
            android.os.Environment.isExternalStorageManager()
        } else {
            // Android 10 及以下使用传统权限
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }

    private fun requestStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11+ 请求 MANAGE_EXTERNAL_STORAGE 权限
            try {
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                intent.data = Uri.parse("package:${MyApplication.context?.packageName}")
                manageStoragePermissionLauncher?.launch(intent)
            } catch (e: Exception) {
                GlobalLog.error(e, "Failed to request manage storage permission")
                // 降级到传统权限请求
                requestLegacyStoragePermission()
            }
        } else {
            requestLegacyStoragePermission()
        }
    }

    private fun requestLegacyStoragePermission() {
        val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 不需要 READ_EXTERNAL_STORAGE，使用 SAF
            arrayOf()
        } else {
            arrayOf(
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            )
        }

        if (permissions.isNotEmpty()) {
            permissionLauncher?.launch(permissions)
        } else {
            // Android 13+ 直接启动文件选择器
            launchFilePicker()
        }
    }

    private fun launchFilePicker() {
        try {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
                addCategory(Intent.CATEGORY_OPENABLE)
                type = "application/json"
                // 也允许选择所有文件类型，以防JSON文件没有正确的MIME类型
                putExtra(Intent.EXTRA_MIME_TYPES, arrayOf("application/json", "text/plain", "*/*"))
            }
            filePickerLauncher?.launch(intent)
        } catch (e: Exception) {
            GlobalLog.error(e, "Failed to launch file picker")
            ViewFunction.showAlertDialog(
                AlertDialogMessage(
                    StringContentVo(
                        Res.string.fileImportError,
                        "Failed to open file picker: ${e.message}"
                    )
                )
            )
        }
    }

    private fun handleSelectedFile(uri: Uri) {
        if (uri.path?.endsWith(".json") == false) {
            ViewFunction.showAlertDialog(
                AlertDialogMessage(
                    StringContentVo(
                        Res.string.invalidJsonFile,
                        ""
                    )
                )
            )
            return
        }
        try {
            val context = MyApplication.context ?: return
            val contentResolver = context.contentResolver

            contentResolver.openInputStream(uri)?.use { inputStream ->
                val reader = BufferedReader(InputStreamReader(inputStream))
                val jsonContent = reader.readText()

                // 获取文件名（可选，用于日志）
                val fileName = getFileName(uri)
                GlobalLog.info("Selected file: $fileName, content length: ${jsonContent.length}")

                // 调用订阅服务导入文件内容
                ViewFunction.setWindowLoading(true)
                SubscribeService.importSubscribeFromJsonContent(jsonContent)
            }
        } catch (e: Exception) {
            GlobalLog.error(e, "Failed to read selected file")
            ViewFunction.showAlertDialog(
                AlertDialogMessage(
                    StringContentVo(
                        Res.string.fileImportError,
                        "Failed to read file: ${e.message}"
                    )
                )
            )
        }
    }

    private fun getFileName(uri: Uri): String? {
        val context = MyApplication.context ?: return null
        return try {
            context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val nameIndex = cursor.getColumnIndex(DocumentsContract.Document.COLUMN_DISPLAY_NAME)
                    if (nameIndex >= 0) cursor.getString(nameIndex) else null
                } else null
            }
        } catch (e: Exception) {
            GlobalLog.error(e, "Failed to get file name")
            null
        }
    }
}
