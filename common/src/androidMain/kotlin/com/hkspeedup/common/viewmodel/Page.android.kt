package com.hkspeedup.common.viewmodel

import android.os.Parcelable
import kotlinx.parcelize.Parcelize


@Parcelize
actual class MainPage : Page, Parcelable
@Parcelize
actual class TunnelMapperInfoPage : Page, Parcela<PERSON>
@Parcelize
actual class VersionInfoPage : Page, Parcela<PERSON>
@Parcelize
actual class SetSubscribeSettingPage : Page, Parcelable
@Parcelize
actual class AppInfoPage : Page, Parcelable

@Parcelize
actual class LocalProxyPortPage : Page, Parcelable
@Parcelize
actual class CustomRuleListPage : Page, Parcelable
@Parcelize
actual class CustomRuleDetailPage : Page, Parcelable

@Parcelize
actual class PortForwardingListPage : Page, Parcelable

@Parcelize
actual class PortForwardingDetailPage : Page, Parcelable

@Parcelize
actual class PortForwardingRemarkPage : Page, Parcelable