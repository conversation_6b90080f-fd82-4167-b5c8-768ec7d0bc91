package com.hkspeedup.common.service

import android.app.Activity
import android.content.Intent
import android.net.VpnService
import android.os.Build
import androidx.activity.result.ActivityResultLauncher
import com.google.gson.JsonObject
import com.hkspeedup.common.MyApplication
import com.hkspeedup.common.lib.FlyShadowCore
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.util.SystemProxyUtil
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.connectFail
import flyshadow.common.generated.resources.pleaseSelectServer
import kotlinx.coroutines.*
import org.apache.commons.lang3.StringUtils


actual object ProxyService {

    private var connectTunnelJob: Job? = null

    // 声明一个布尔值的回调函数
    private var loginSuccess: Boolean = false

    actual fun startProxy(callback: (message: String?) -> Unit) {
        GlobalScope.launch {
            // 定义一个变量 i
            var port = config.proxyPort
            var errorMessage: String? = null
            for (i in 0 until 5) {
                port += i
                errorMessage = FlyShadowCore.startProxy(port)
                if (StringUtils.isBlank(errorMessage)) {
                    // 保存配置
                    config.proxyPort = port
                    ConfigService.save()
                    // 回调
                    callback(null)
                    return@launch
                }
                delay(1000)
            }
            callback(errorMessage)
        }
    }

    actual fun stopProxy() {}

    // 测试隧道时延函数
    @OptIn(DelicateCoroutinesApi::class)
    // 完成之后的回调函数
    actual fun testTunnelDelay(finish: (Long, String) -> Unit) {
        // 输出开始的日志
        GlobalLog.info("Ping start")

        config.subscribeInfo?.node?.forEach { node ->
            val jsonObject = JsonObject()
            jsonObject.addProperty("host", node.host)
            jsonObject.addProperty("port", node.port)
            jsonObject.addProperty("password", node.password)
            GlobalScope.launch {
                runCatching {
                    finish(FlyShadowCore.testPingTunnel(jsonObject), node.name!!)
                }.onFailure {
                    finish(-1, node.name!!)
                    GlobalLog.error(it, "${node.name} Ping Error")
                }
                // 输出测试结束的日志
                GlobalLog.info("${node.name} Ping end")
            }
        }
    }


    // 开启隧道函数
    @OptIn(DelicateCoroutinesApi::class)
    actual fun startTunnel() {
        stopTunnel {
            connectTunnelJob?.cancel()
            connectTunnelJob = GlobalScope.launch(Dispatchers.IO) {
                try {
                    // 获取节点信息
                    val selectNode = config.getSelectNodeInfo()
                    // 如果节点信息为空，提示选择节点
                    if (selectNode == null) {
                        ViewFunction.showAlertDialog(
                            AlertDialogMessage(
                                StringContentVo(
                                    Res.string.pleaseSelectServer,
                                    null
                                )
                            )
                        )
                        ViewFunction.setProxyStatus(ProxyStatus.STOP)
                        MyApplication.PROXY_STATUS = ProxyStatus.STOP
                    } else {
                        // 如果节点信息存在，开始连接
                        ViewFunction.setProxyStatus(ProxyStatus.CONNECTING)
                        MyApplication.PROXY_STATUS = ProxyStatus.CONNECTING

                        // 设置原生UDP
                        FlyShadowCore.setNativeUdp(config.nativeUdp && selectNode.nativeUdp?.toInt() == 1)
                        // 设置直连优先参数
                        FlyShadowCore.setDirectConnPriority(
                            config.directConnPriority,
                            config.directConnPriorityTimeout
                        )
                        // 内置DNS
                        FlyShadowCore.setUseBuildInDns(config.useBuildInDns)

                        // 设置代理类型
                        when (config.proxyType) {
                            0 -> {
                                // 直连模式
                                FlyShadowCore.setProxyType(0)
                            }

                            1 -> {
                                // 全局模式
                                FlyShadowCore.setProxyType(2)
                            }

                            2 -> {
                                // 规则模式
                                FlyShadowCore.setProxyType(3)
                            }

                            else -> {}
                        }

                        // 设置代理规则
                        ConfigService.loadDomainRuleList()
                        // 连接隧道
                        FlyShadowCore.connectTunnel(selectNode.host!!, selectNode.port!!, selectNode.password!!)

                        for (i in 0 until 5) {
                            if (FlyShadowCore.getTunnelStatus() == 0) {
                                ViewFunction.setProxyStatus(ProxyStatus.START)
                                MyApplication.PROXY_STATUS = ProxyStatus.START
                                SystemProxyUtil.enableProxy("127.0.0.1", config.proxyPort)
                                loginSuccess = true
                                startVpnService()
                                break
                            }
                            delay(1000)
                        }

                        // 连接不成功
                        if (!loginSuccess) {
                            FlyShadowCore.closeTunnel()
                            ViewFunction.setProxyStatus(ProxyStatus.STOP)
                            MyApplication.PROXY_STATUS = ProxyStatus.STOP
                            ViewFunction.showAlertDialog(
                                AlertDialogMessage(
                                    StringContentVo(
                                        Res.string.connectFail,
                                        null
                                    )
                                )
                            )
                            GlobalLog.info("Connect Fail")
                            return@launch
                        }

                        // 获取隧道状态
                        while (loginSuccess) {
                            if (FlyShadowCore.getTunnelStatus() == 0) {
                                MyApplication.PROXY_STATUS = ProxyStatus.START
                                ViewFunction.setProxyStatus(ProxyStatus.START)
                                // 获取隧道流量
                                ViewFunction.setUploadTraffic(FlyShadowCore.getTunnelUpload().toInt())
                                ViewFunction.setDownloadTraffic(FlyShadowCore.getTunnelDownload().toInt())
                            } else {
                                MyApplication.PROXY_STATUS = ProxyStatus.CONNECTING
                                ViewFunction.setProxyStatus(ProxyStatus.CONNECTING)
                            }

                            delay(1000)
                        }
                    }
                } catch (e: CancellationException) {
                    GlobalLog.info("ConnectTunnelJob Cancel")
                } catch (e: Exception) {
                    stopTunnel {}
                    GlobalLog.error(e, "Start Tunnel error")
                }
            }
        }
    }

    // 停止隧道函数
    @OptIn(DelicateCoroutinesApi::class)
    actual fun stopTunnel(stopFinish: () -> Unit) {
        // 先设置等待状态
        ViewFunction.setProxyStatus(ProxyStatus.CONNECTING)
        MyApplication.PROXY_STATUS = ProxyStatus.CONNECTING

        GlobalScope.launch(Dispatchers.IO) {
            delay(500)
            loginSuccess = false
            connectTunnelJob?.cancel()
            connectTunnelJob = null
            // 停止登录成功的标志
            stopVpnService()
            // 重设上传和下载数据
            ViewFunction.setUploadTraffic(0)
            ViewFunction.setDownloadTraffic(0)
            // 关闭隧道连接
            FlyShadowCore.closeTunnel()
            // 停止代理状态
            ViewFunction.setProxyStatus(ProxyStatus.STOP)
            MyApplication.PROXY_STATUS = ProxyStatus.STOP
            stopFinish()
        }
    }

    // activityResultCallback是一个函数变量，用于处理VPN服务启动时的结果。如果结果是Activity.RESULT_OK，就启动VPN服务；否则停止隧道。
    var activityResultCallback: (Int) -> Unit = {
        if (it == Activity.RESULT_OK) {
            MyApplication.context!!.startService(
                Intent(MyApplication.context, MyVpnService::class.java).apply {
                    action = MyVpnService.Companion.VpnAction.START.name
                }
            )
        } else {
            stopTunnel {}
        }
    }

    // registerForActivityResult是一个ActivityResultLauncher，用于在Activity中注册处理结果的回调。
    lateinit var registerForActivityResult: ActivityResultLauncher<Intent>

    // startVpnService用于启动VPN服务，首先尝试预备VPN服务，如果预备成功则注册ActivityResultLauncher，否则直接启动VPN服务。
    private fun startVpnService() {
        val intent = VpnService.prepare(MyApplication.context)
        if (intent == null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                MyApplication.context!!.startForegroundService(
                    Intent(MyApplication.context, MyVpnService::class.java).apply {
                        action = MyVpnService.Companion.VpnAction.START.name
                    }
                )
            } else {
                MyApplication.context!!.startService(
                    Intent(MyApplication.context, MyVpnService::class.java).apply {
                        action = MyVpnService.Companion.VpnAction.START.name
                    }
                )
            }
        } else {
            registerForActivityResult.launch(intent)
        }
    }

    // stopVpnService用于停止VPN服务，首先尝试预备VPN服务，如果预备成功则直接停止VPN服务。
    private fun stopVpnService() {
        val intent = VpnService.prepare(MyApplication.context)
        if (intent == null) {
            MyApplication.context!!.startService(
                Intent(
                    MyApplication.context,
                    MyVpnService::class.java
                ).apply {
                    action = MyVpnService.Companion.VpnAction.STOP.name
                }
            )
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    actual fun reSelectTunnel() {
        GlobalLog.info("Reselect Tunnel")
        // 获取节点信息
        val selectNode = config.getSelectNodeInfo()
        // 如果节点信息为空，提示选择节点
        if (selectNode == null) {
            stopTunnel {}
            ViewFunction.showAlertDialog(
                AlertDialogMessage(
                    StringContentVo(
                        Res.string.pleaseSelectServer,
                        null
                    )
                )
            )
        } else if (connectTunnelJob != null) {
            FlyShadowCore.closeTunnel()
            GlobalScope.launch(Dispatchers.IO) {
                // 设置原生UDP
                FlyShadowCore.setNativeUdp(config.nativeUdp && selectNode.nativeUdp?.toInt() == 1)
                // 连接
                FlyShadowCore.connectTunnel(selectNode.host!!, selectNode.port!!, selectNode.password!!)
            }
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    actual fun restartTunnel() {
        stopTunnel {
            GlobalScope.launch(Dispatchers.IO) {
                var forTime = 0
                while (forTime++ < 10) {
                    delay(500)
                    if (MyVpnService.VPN_ACTION == MyVpnService.Companion.VpnAction.STOP) {
                        startTunnel()
                        return@launch
                    }
                }
            }
        }

    }
}