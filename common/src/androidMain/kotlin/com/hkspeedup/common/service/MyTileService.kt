package com.hkspeedup.common.service

import android.content.Intent
import android.graphics.drawable.Icon
import android.os.Build
import android.os.IBinder
import android.service.quicksettings.Tile
import android.service.quicksettings.TileService
import android.util.Log
import androidx.annotation.RequiresApi
import com.hkspeedup.common.MyApplication
import com.hkspeedup.common.MyApplication.Companion.context
import com.hkspeedup.common.R
import com.hkspeedup.common.viewmodel.ProxyStatus
import kotlinx.coroutines.*

@RequiresApi(Build.VERSION_CODES.N)
class MyTileService : TileService() {

    companion object{
        var LAUNCH_ON_START_JOB:Job? = null
    }

    private var job: Job? = null

    override fun onDestroy() {
        Log.i("MyTileService", "onDestroy")
        super.onDestroy()
    }

    override fun onBind(intent: Intent?): IBinder? {
        LAUNCH_ON_START_JOB?.cancel()
        Log.i("MyTileService", "onBind")
        return super.onBind(intent)
    }

    override fun onTileAdded() {
        Log.i("MyTileService", "onTileAdded")
        super.onTileAdded()
    }

    override fun onTileRemoved() {
        Log.i("MyTileService", "onTileRemoved")
        super.onTileRemoved()
    }

    @OptIn(DelicateCoroutinesApi::class)
    override fun onStartListening() {
        Log.i("MyTileService", "onStartListening,qsTile.state: " + qsTile.state)
        super.onStartListening()
        qsTile.label = getString(R.string.app_name)
        qsTile.icon = Icon.createWithResource(context, R.mipmap.ic_launcher)

        job?.cancel()
        job = GlobalScope.launch(Dispatchers.IO) {
            while (true) {
                when (MyApplication.PROXY_STATUS) {
                    ProxyStatus.START -> {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            qsTile.subtitle = "Enable"
                        }
                        qsTile.state = Tile.STATE_ACTIVE
                    }

                    ProxyStatus.STOP -> {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            qsTile.subtitle = "Disable"
                        }
                        qsTile.state = Tile.STATE_INACTIVE
                    }

                    else -> {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                            qsTile.subtitle = "Connecting"
                        }
                        qsTile.state = Tile.STATE_UNAVAILABLE
                    }
                }
                GlobalScope.launch(Dispatchers.Main) {
                    qsTile.updateTile()
                }
                delay(300)
            }
        }

    }

    override fun onStopListening() {
        Log.i("MyTileService", "onStopListening")
        job?.cancel()
        super.onStopListening()
    }

    override fun onClick() {
        Log.i("MyTileService", "onClick")

        val tile = qsTile ?: return
        when (tile.state) {
            Tile.STATE_INACTIVE -> {
                ProxyService.startTunnel()
            }

            Tile.STATE_ACTIVE -> {
                ProxyService.stopTunnel{}
            }
        }

    }

}