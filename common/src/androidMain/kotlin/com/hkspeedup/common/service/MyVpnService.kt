package com.hkspeedup.common.service

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.IpPrefix
import android.net.ProxyInfo
import android.net.VpnService
import android.os.Build
import android.os.ParcelFileDescriptor
import androidx.core.app.NotificationCompat
import cn.hutool.core.collection.CollUtil
import cn.hutool.core.util.RandomUtil
import com.hkspeedup.common.MyApplication
import com.hkspeedup.common.R
import com.hkspeedup.common.lib.FlyShadowCore
import com.hkspeedup.common.util.GlobalLog
import java.io.FileInputStream
import java.io.FileOutputStream
import java.net.InetAddress
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors


class MyVpnService : VpnService() {

    companion object {

        var VPN_ACTION = VpnAction.STOP

        enum class VpnAction {
            STOP, START
        }
    }


    private val channelId = RandomUtil.randomString(10)

    private var vpnInterface: ParcelFileDescriptor? = null

    private var executorService: ExecutorService? = null

    private fun startDeviceTunnel() {
        vpnInterface?.close()
        // Configure a new interface from our VpnService instance. This must be done
        // from inside a VpnService.
        val builder = Builder()

        if (CollUtil.isNotEmpty(config.apps)) {
            if (config.excludeAppsMode == 0) {
                // 禁止自己走vpn
                builder.addDisallowedApplication(MyApplication.context!!.packageName)
                for (app in config.apps) {
                    kotlin.runCatching { builder.addDisallowedApplication(app) }
                }
            } else if (config.excludeAppsMode == 1) {
                for (app in config.apps) {
                    if (app == MyApplication.context!!.packageName) {
                        continue
                    }
                    kotlin.runCatching { builder.addAllowedApplication(app) }
                }
            } else {
                builder.addDisallowedApplication(MyApplication.context!!.packageName)
            }
        } else {
            builder.addDisallowedApplication(MyApplication.context!!.packageName)
        }

        // Create a local TUN interface using predetermined addresses. In your app,
        // you typically use values returned from the VPN gateway during handshaking.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(10, 0, 0, 0)), 8))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 16, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 17, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 18, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 19, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 20, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 21, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 22, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 23, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 24, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 25, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 26, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 27, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 28, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 29, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 30, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(172.toByte(), 31, 0, 0)), 16))
            builder.excludeRoute(IpPrefix(InetAddress.getByAddress(byteArrayOf(192.toByte(), 168.toByte(), 0, 0)), 16))
            GlobalLog.info("excludeRoute")
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            builder.setHttpProxy(ProxyInfo.buildDirectProxy("127.0.0.1", config.proxyPort))
        }
        vpnInterface = builder
            .addAddress("***********", 24)
            .addRoute("0.0.0.0", 0)
            .addRoute("**********", 19)
            .addDnsServer("*******")
            .addDnsServer("*******")
            .setSession("FlyShadow")
            .setBlocking(true)
            .establish()
        GlobalLog.info("start Tunnel: $vpnInterface")
        VPN_ACTION = VpnAction.START
    }

    private fun stopDeviceTunnel() {
        executorService?.shutdownNow()
        vpnInterface?.close()
        vpnInterface = null

        VPN_ACTION = VpnAction.STOP
    }

    override fun onRevoke() {
        GlobalLog.info("MyVpnService onRevoke")
        stopForeground()
        stopDeviceTunnel()
        stopSelf()
    }

    override fun onDestroy() {
        super.onDestroy()
        GlobalLog.info("MyVpnService onDestroy")
        stopForeground()
        stopDeviceTunnel()
        stopSelf()
    }

    @SuppressLint("WrongConstant")
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        GlobalLog.info("MyVpnService intent?.action: ${intent?.action}")
        if (intent?.action == VpnAction.START.name) {
            startDeviceTunnel()
            startForeground()

            val outputStream = FileOutputStream(vpnInterface?.fileDescriptor)
            val inputStream = FileInputStream(vpnInterface?.fileDescriptor)

            executorService?.shutdownNow()
            executorService = Executors.newFixedThreadPool(4)
            executorService!!.submit {
                runCatching {
                    while (true) {
                        FlyShadowCore.getTunData {
                            outputStream.write(it)
                            outputStream.flush()
                        }
                    }
                }.onFailure {
                    GlobalLog.error(it, "getTunData Error")
                    ProxyService.stopTunnel { }
                }
            }
            executorService!!.submit {
                runCatching {
                    val buffer = ByteArray(8192)  // 8 KB 缓冲区大小
                    var length: Int
                    while (true) {
                        // 从 VPN 服务中读取输入流
                        length = inputStream.read(buffer)

                        if (length <= 0) continue

                        FlyShadowCore.sendToTun(buffer, length)
                    }
                }.onFailure {
                    GlobalLog.error(it, "sendToTun Error")
                    ProxyService.stopTunnel { }
                }
            }

            return START_STICKY
        } else {
            GlobalLog.info("onStartCommand onDestroy")
            stopForeground()
            stopDeviceTunnel()
            stopSelf()
            return START_NOT_STICKY
        }
    }

    private fun getAppName(): String {
        try {
            val packageManager = packageManager
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
            return packageManager.getApplicationLabel(applicationInfo).toString()
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return ""
    }

    private fun stopForeground() {
        // 关闭前台服务，并移除通知
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Android 8.0 (API 26)及更高版本使用stopForeground(int flags)
            stopForeground(STOP_FOREGROUND_REMOVE)
        } else {
            // Android 7.1及以下版本使用stopForeground(boolean removeNotification)
            stopForeground(true) // true表示移除通知
        }

        // 移除通知渠道（仅在Android O及以上版本执行）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.deleteNotificationChannel(channelId)
        }
    }

    private fun startForeground() {
        val notificationManager = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            getSystemService(NotificationManager::class.java) as NotificationManager
        } else {
            getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        }

        // Check if the device is running on Android O or higher to create a notification channel.
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channelName = getAppName()
            val importance = NotificationManager.IMPORTANCE_DEFAULT
            val notificationChannel = NotificationChannel(channelId, channelName, importance)
            // Customize the notification channel settings if needed.
            // notificationChannel.description = "Your Channel Description"
            // notificationChannel.enableVibration(false)
            // ...

            notificationManager.createNotificationChannel(notificationChannel)
        }

        // Create a notification
        val notificationBuilder = NotificationCompat.Builder(this, channelId)
            .setOngoing(true)
            .setContentTitle("FlyShadow Launching")
            .setSmallIcon(R.mipmap.ic_launcher)
            .setPriority(NotificationCompat.PRIORITY_MIN)
            .setCategory(Notification.CATEGORY_SERVICE)
        // Add any other configuration to the notification as needed.
        // .setContentTitle("Your Title")
        // .setContentText("Your Text")
        // ...

        val notification = notificationBuilder.build()

        // Set the app to the foreground with the created notification.
        startForeground(1, notification)
    }

}