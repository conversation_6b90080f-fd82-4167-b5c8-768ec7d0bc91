package com.hkspeedup.common.lib

import cn.hutool.core.util.ReflectUtil
import com.google.gson.JsonObject
import com.hkspeedup.common.ui.OnSupportWin10Str
import com.hkspeedup.common.util.*
import com.sun.jna.Library
import com.sun.jna.Memory
import com.sun.jna.Native
import com.sun.jna.Pointer
import java.util.logging.*


object FlyShadowCore {
    private var rt: Long? = null
    private var tc: Long? = null
    private var p: Long? = null
    private var t: Long? = null
    private var win_tun_env_prt: Long? = null
    private var tun_server_env_prt: Long? = null

    fun initRuntime() {
        if (rt != null) {
            return
        }
        GlobalLog.info("initRuntime")
        rt = FlyShadowCoreInterop.getInstance().new_runtime()
    }

    fun initTunnelContext() {
        if (tc != null) {
            return
        }
        GlobalLog.info("initTunnelContext")
        tc = FlyShadowCoreInterop.getInstance().new_tunnel_context(rt!!)
    }

    fun initFinish(): Boolean {
        return tc != null && rt != null
    }

    fun setDomainRuleObj(domain: String, matching: Int, proxyType: Int, directConnPriority: Int) {
        FlyShadowCoreInterop.getInstance()
            .set_domain_rule_obj(rt!!, tc!!, domain, matching, proxyType, directConnPriority)
    }

    fun clearDomainRule() {
        FlyShadowCoreInterop.getInstance().clear_domain_rule(rt!!, tc!!)
    }

    fun setGeoIP(country: String, bytes: ByteArray) {
        val input = Memory(bytes.size.toLong())
        input.write(0, bytes, 0, bytes.size)
        FlyShadowCoreInterop.getInstance().set_geoip(rt!!, tc!!, country, input, bytes.size)
    }

    fun closeConnect(key: String) {
        FlyShadowCoreInterop.getInstance().close_connect(rt!!, tc!!, key)
    }

    fun setProxyType(index: Int) {
        FlyShadowCoreInterop.getInstance().set_proxy_type(rt!!, tc!!, index)
    }

    fun setUdpProxyType(index: Int) {
        FlyShadowCoreInterop.getInstance().set_udp_proxy_type(rt!!, tc!!, index)
    }

    fun setFakeIp(enable: Boolean) {
        FlyShadowCoreInterop.getInstance().set_fake_ip(rt!!, tc!!, enable)
    }

    fun setNativeUdp(enable: Boolean) {
        FlyShadowCoreInterop.getInstance().set_native_udp(rt!!, tc!!, enable)
    }

    fun setDirectConnPriority(enable: Boolean, timeout: Long) {
        FlyShadowCoreInterop.getInstance().set_direct_conn_priority(rt!!, tc!!, enable, timeout)
    }

    fun setTunModeEnable(enable: Boolean) {
        FlyShadowCoreInterop.getInstance().set_tun_mode_enable(
            rt!!,
            tc!!,
            enable
        )
    }

    fun setUseBuildInDns(enable: Boolean) {
        FlyShadowCoreInterop.getInstance().set_use_build_in_dns(
            rt!!,
            tc!!,
            enable
        )
    }

    fun setIPv6Enable(enable: Boolean) {
        FlyShadowCoreInterop.getInstance().set_ipv6_enable(
            rt!!,
            tc!!,
            enable
        )
    }

    fun getTunnelMapperInfoJson(): String {
        val pointer = FlyShadowCoreInterop.getInstance().get_tunnel_mapper_info_json(rt!!, tc!!)
        val string = pointer.getString(0, "UTF-8")
        FlyShadowCoreInterop.getInstance().free_string(pointer)
        return string
    }

    fun startProxy(port: Int): String {
        GlobalLog.info("startProxy")
        stopProxy()
        p = FlyShadowCoreInterop.getInstance().new_proxy(tc!!)
        val pointer = FlyShadowCoreInterop.getInstance().start_proxy(rt!!, p!!, port)
        val string = pointer.getString(0, "UTF-8")
        FlyShadowCoreInterop.getInstance().free_string(pointer)
        return string
    }

    fun stopProxy() {
        p?.let {
            FlyShadowCoreInterop.getInstance().stop_proxy(rt!!, it)
            FlyShadowCoreInterop.getInstance().free_proxy(it)
        }
    }

    fun testPingTunnel(connectInfo: JsonObject): Long {
        val jsonArray = GsonUtil.createGson().toJson(connectInfo)
        return FlyShadowCoreInterop.getInstance().test_tunnel_delay(
            rt!!,
            jsonArray.toString()
        )
    }

    fun connectTunnel(host: String, port: Int, password: String) {
        FlyShadowCoreInterop.getInstance().connect_tunnel(rt!!, tc!!, host, port, password)
    }

    fun closeTunnel() {
        FlyShadowCoreInterop.getInstance().close_tunnel(rt!!, tc!!)
    }

    fun getTunnelUpload(): Long {
        return FlyShadowCoreInterop.getInstance().get_tunnel_upload(rt!!, tc!!)
    }

    fun getTunnelDownload(): Long {
        return FlyShadowCoreInterop.getInstance().get_tunnel_download(rt!!, tc!!)
    }

    fun getTunnelPingDelay(): Int {
        return FlyShadowCoreInterop.getInstance().get_tunnel_ping_delay(rt!!, tc!!)
    }

    fun getTunnelStatus(): Int {
        return FlyShadowCoreInterop.getInstance().get_tunnel_status(rt!!, tc!!)
    }

    fun newTun() {
        if (t == null) t = FlyShadowCoreInterop.getInstance().new_tun(rt!!, tc!!)
    }

    fun sendToTun(byteArray: ByteArray, len: Int) {
        t?.let {
            val input = Memory(len.toLong())
            input.write(0, byteArray, 0, len)
            FlyShadowCoreInterop.getInstance().send_to_tun(rt!!, it, input, len)
        }
    }

    fun getTunData(byte: (ByteArray) -> Unit) {
        t?.let {
            val outputSize: Pointer = Memory(Native.SIZE_T_SIZE.toLong())
            val outPutPtr = FlyShadowCoreInterop.getInstance().get_tun_data(rt!!, it, outputSize)
            val len = outputSize.getInt(0)
            val byteArray = outPutPtr.getByteArray(0, len)
            byte(byteArray)
            FlyShadowCoreInterop.getInstance().free_tun_data(outPutPtr)
        }
    }

    fun newWinTunEnv(winTunLibPath: String) {
        tc?.let {
            win_tun_env_prt = FlyShadowCoreInterop.getInstance().new_win_tun_env(it, winTunLibPath)
        }
    }

    fun openWinTun(): String? {
        win_tun_env_prt?.let {
            val pointer = FlyShadowCoreInterop.getInstance().open_win_tun(it)
            val string = pointer.getString(0, "UTF-8")
            FlyShadowCoreInterop.getInstance().free_string(pointer)
            return string
        }
        return null
    }

    fun startWinTunExchange(): String? {
        win_tun_env_prt?.let {
            val pointer = FlyShadowCoreInterop.getInstance().start_win_tun_exchange(it)
            val string = pointer.getString(0, "UTF-8")
            FlyShadowCoreInterop.getInstance().free_string(pointer)
            return string
        }
        return null
    }

    fun stopWinTun() {
        win_tun_env_prt?.let {
            FlyShadowCoreInterop.getInstance().stop_win_tun(it)
        }
    }

    fun setClientUuid(uuid: String) {
        FlyShadowCoreInterop.getInstance().set_client_uuid(rt!!, tc!!, uuid)
    }

    fun addPortForwarding(port: Int, targetAddr: String, uuid: String): String {
        synchronized(this) {
            val pointer = FlyShadowCoreInterop.getInstance().add_port_forwarding(rt!!, tc!!, port, targetAddr, uuid)
            val string = pointer.getString(0, "UTF-8")
            FlyShadowCoreInterop.getInstance().free_string(pointer)
            return string
        }
    }

    fun removePortForwarding(port: Int) {
        FlyShadowCoreInterop.getInstance().remove_port_forwarding(rt!!, tc!!, port)
    }

    fun clearPortForwarding() {
        synchronized(this) {
            FlyShadowCoreInterop.getInstance().clear_port_forwarding(rt!!, tc!!)
        }
    }

    fun newTunServerEnv() {
        tc?.let {
            if (tun_server_env_prt == null) {
                tun_server_env_prt = FlyShadowCoreInterop.getInstance().new_tun_server_env(it)
            }
        }
    }

    fun startTunServer(): String? {
        tun_server_env_prt?.let {
            val pointer = FlyShadowCoreInterop.getInstance().start_tun_server(it)
            val string = pointer.getString(0, "UTF-8")
            FlyShadowCoreInterop.getInstance().free_string(pointer)
            return string
        }
        return "Env not init"
    }

    fun stopTunServer() {
        tun_server_env_prt?.let {
            FlyShadowCoreInterop.getInstance().stop_tun_server(it)
        }
    }
}

interface FlyShadowCoreInterop : Library {

    companion object {
        private var flyShadowCoreInterop: FlyShadowCoreInterop? = null
        val getInstance = {
            if (flyShadowCoreInterop == null) {
                flyShadowCoreInterop = load()
            }
            flyShadowCoreInterop!!
        }

        /**
         * 加载 FlyShadowCoreInterop 实例。
         * 设置系统属性以启用 JNA 加载和编码调试，并配置日志处理程序。
         *
         * @return 加载的 FlyShadowCoreInterop 实例。
         */
        private fun load(): FlyShadowCoreInterop {
            System.setProperty("jna.debug_load", "true")
            System.setProperty("jna.debug_load.jna", "true")
            System.setProperty("native.encoding", "UTF-8")

            kotlin.runCatching {
                val handler = object : Handler() {
                    val simpleFormatter = SimpleFormatter()

                    override fun publish(record: LogRecord?) {
                        val level = record!!.level
                        when (level) {
                            Level.SEVERE -> GlobalLog.error(simpleFormatter.format(record))
                            else -> GlobalLog.info(simpleFormatter.format(record))
                        }
                    }

                    override fun flush() {}

                    override fun close() {}
                }

                val forName = Class.forName("com.sun.jna.Native")
                val field = ReflectUtil.getField(forName, "LOG")
                val staticFieldValue = ReflectUtil.getStaticFieldValue(field) as Logger
                staticFieldValue.addHandler(handler)
            }.onFailure {
                GlobalLog.error(it, "Set Native Logger error")
            }

            if (SystemUtil.isWindows7()) {
                throw RuntimeException(OnSupportWin10Str)
            }

            val flyShadowCoreInterop = Native.load("flyshadow_core_lib", FlyShadowCoreInterop::class.java)
            GlobalLog.error("Load FlyShadow Core Success")
            return flyShadowCoreInterop
        }
    }

    /**
     * 创建新的运行环境。
     *
     * @return 新建的运行环境的句柄。
     */
    fun new_runtime(): Long

    /**
     * 创建新的隧道上下文。
     *
     * @param rt 运行环境。
     * @return 指向新建隧道上下文的指针。
     */
    fun new_tunnel_context(rt: Long): Long

    /**
     * 设置隧道上下文的域名规则。
     *
     * @param rt   运行环境。
     * @param tc   隧道上下文。
     * @param domain 要设置的域名。
     * @param matching 要设置的匹配模式。
     * @param proxyType 要设置的代理类型。
     * @param directConnPriority 是否直连优先。
     */
    fun set_domain_rule_obj(rt: Long, tc: Long, domain: String, matching: Int, proxyType: Int, directConnPriority: Int)

    /**
     * 清空隧道上下文的域名规则。
     *
     * @param rt   运行环境。
     * @param tc   隧道上下文。
     */
    fun clear_domain_rule(rt: Long, tc: Long)

    /**
     * 为隧道上下文设置 GeoIP 信息。
     *
     * @param rt    运行环境。
     * @param tc    隧道上下文。
     * @param key   GeoIP 键。
     * @param input     输入数据的指针。
     * @param inputSize 输入数据的大小。
     */
    fun set_geoip(rt: Long, tc: Long, key: String, input: Pointer, inputSize: Int)

    /**
     * 关闭连接
     *
     * @param rt    运行环境。
     * @param tc    隧道上下文。
     * @param key   连接的Key
     */
    fun close_connect(rt: Long, tc: Long, key: String)

    /**
     * 设置代理类型。
     *
     * @param rt        运行环境。
     * @param tc        隧道上下文。
     * @param typeIndex 代理类型索引。
     */
    fun set_proxy_type(rt: Long, tc: Long, typeIndex: Int)

    /**
     * 设置UDP代理类型。
     *
     * @param rt        运行环境。
     * @param tc        隧道上下文。
     * @param typeIndex 代理类型索引。
     */
    fun set_udp_proxy_type(rt: Long, tc: Long, typeIndex: Int)

    /**
     * 设置 DNS IP 缓存的开关。
     *
     * @param rt     运行环境。
     * @param tc     隧道上下文。
     * @param enable DNS IP 缓存开关。
     */
    fun set_fake_ip(rt: Long, tc: Long, enable: Boolean)

    /**
     * 设置原生UDP开关。
     *
     * @param rt     运行环境。
     * @param tc     隧道上下文。
     * @param enable DNS IP 缓存开关。
     */
    fun set_native_udp(rt: Long, tc: Long, enable: Boolean)

    /**
     * 设置IPv6开关。
     *
     * @param rt     运行环境。
     * @param tc     隧道上下文。
     * @param enable DNS IP 缓存开关。
     */
    fun set_ipv6_enable(rt: Long, tc: Long, enable: Boolean)

    /**
     * 设置直连优先参数。
     *
     * @param rt     运行环境。
     * @param tc     隧道上下文。
     * @param enable 开关
     * @param enable 超时时间
     */
    fun set_direct_conn_priority(rt: Long, tc: Long, enable: Boolean, timeout: Long)

    /**
     * 设置TUN模式启用
     *
     * @param rt               运行环境。
     * @param tc               隧道上下文。
     * @param enable           开关。
     */
    fun set_tun_mode_enable(
        rt: Long,
        tc: Long,
        enable: Boolean
    )

    /**
     * 设置使用内置DNS
     *
     * @param rt               运行环境。
     * @param tc               隧道上下文。
     * @param enable           开关。
     */
    fun set_use_build_in_dns(
        rt: Long,
        tc: Long,
        enable: Boolean
    )

    /**
     * 获取隧道映射信息
     *
     * @param rt               运行环境。
     * @param tc               隧道上下文。
     * @param enable           本地接口 IP开关。
     * @param localInterfaceIp 本地接口 IP。
     */
    fun get_tunnel_mapper_info_json(rt: Long, tc: Long): Pointer

    /**
     * 创建新的代理。
     *
     * @param tc   隧道上下文。
     * @return 指向代理的指针。
     */
    fun new_proxy(tc: Long): Long

    /**
     * 启动代理。
     *
     * @param rt 运行环境。
     * @param p  代理指针。
     * @param port 代理端口。
     * @return 错误内存的字符串指针。
     */
    fun start_proxy(rt: Long, p: Long, port: Int): Pointer

    /**
     * 停止代理。
     *
     * @param rt 运行环境。
     * @param p  代理指针。
     */
    fun stop_proxy(rt: Long, p: Long)

    /**
     * 连接隧道。
     *
     * @param rt       运行环境。
     * @param tc       隧道上下文。
     * @param host     主机地址。
     * @param port     端口。
     * @param password 密码。
     * @return 错误内存的字符串指针。
     */
    fun connect_tunnel(rt: Long, tc: Long, host: String, port: Int, password: String)

    /**
     * 测试隧道延迟。
     *
     * @param rt           运行环境。
     * @param connect_info  隧道列表。
     * @return 延迟列表指针。
     */
    fun test_tunnel_delay(rt: Long, connect_info: String): Long

    /**
     * 关闭隧道。
     *
     * @param rt 运行环境。
     * @param tc 隧道上下文。
     */
    fun close_tunnel(rt: Long, tc: Long)

    /**
     * 获取隧道上传数据。
     *
     * @param rt 运行环境。
     * @param tc 隧道上下文。
     * @return 上传数据。
     */
    fun get_tunnel_upload(rt: Long, tc: Long): Long

    /**
     * 获取隧道下载数据。
     *
     * @param rt 运行环境。
     * @param tc 隧道上下文。
     * @return 下载数据。
     */
    fun get_tunnel_download(rt: Long, tc: Long): Long

    /**
     * 获取隧道的 PING 延迟。
     *
     * @param rt 运行环境。
     * @param tc 隧道上下文。
     * @return PING 延迟。
     */
    fun get_tunnel_ping_delay(rt: Long, tc: Long): Int

    /**
     * 获取隧道的状态。
     *
     * @param rt 运行环境。
     * @param tc 隧道上下文。
     * @return 隧道状态。
     */
    fun get_tunnel_status(rt: Long, tc: Long): Int

    /**
     * 释放字符串内存。
     *
     * @param ptr 字符串指针。
     */
    fun free_string(ptr: Pointer?)

    /**
     * 释放代理资源。
     *
     * @param p 代理指针。
     */
    fun free_proxy(p: Long)


    /**
     * 释放隧道数据内存。
     *
     * @param x 隧道数据指针。
     */
    fun free_tun_data(x: Pointer)

    /**
     * 创建新的 TUN。
     *
     * @param rt 运行环境。
     * @param tc 隧道上下文。
     * @return 新建 TUN 的指针。
     */
    fun new_tun(rt: Long, tc: Long): Long

    /**
     * 将数据发送到 TUN。
     *
     * @param rt        运行环境。
     * @param t         TUN 指针。
     * @param input     输入数据的指针。
     * @param inputSize 输入数据的大小。
     */
    fun send_to_tun(rt: Long, t: Long, input: Pointer, inputSize: Int)

    /**
     * 获取 TUN 数据。
     *
     * @param rt         运行环境。
     * @param t          TUN 指针。
     * @param outPutSize 输出数据的大小指针。
     * @return TUN 数据的指针。
     */
    fun get_tun_data(rt: Long, t: Long, outPutSize: Pointer): Pointer

    /**
     * 创建新的 Windows TUN 环境。
     *
     * @param tc             隧道上下文。
     * @param win_tun_lib_path Windows TUN 库路径。
     * @return 新建的 Windows TUN 环境指针。
     */
    fun new_win_tun_env(tc: Long, win_tun_lib_path: String): Long

    /**
     * 打开 TUN。
     *
     * @param win_tun_env_ptr Windows TUN 环境指针。
     * @return 打开的 TUN 指针。
     */
    fun open_win_tun(win_tun_env_ptr: Long): Pointer

    /**
     * 开始 TUN 交换。
     *
     * @param win_tun_env_ptr Windows TUN 环境指针。
     * @return 操作结果指针。
     */
    fun start_win_tun_exchange(win_tun_env_ptr: Long): Pointer

    /**
     * 停止 TUN。
     *
     * @param win_tun_env_ptr Windows TUN 环境指针。
     */
    fun stop_win_tun(win_tun_env_ptr: Long)

    /**
     * 设置客户端ID
     *
     * @param rt    运行环境。
     * @param tc    隧道上下文。
     * @param uuid   客户端ID
     */
    fun set_client_uuid(rt: Long, tc: Long, uuid: String)

    /**
     * 添加端口转发
     *
     * @param rt    运行环境。
     * @param tc    隧道上下文。
     * @param port  端口
     * @param target_addr 目标地址
     * @param uuid   客户端ID
     */
    fun add_port_forwarding(rt: Long, tc: Long, port: Int, target_addr: String, uuid: String): Pointer

    /**
     * 删除端口转发
     *
     * @param rt    运行环境。
     * @param tc    隧道上下文。
     * @param port  端口
     */
    fun remove_port_forwarding(rt: Long, tc: Long, port: Int)

    /**
     * 清空端口转发
     *
     * @param rt    运行环境。
     * @param tc    隧道上下文。
     */
    fun clear_port_forwarding(rt: Long, tc: Long)

    /**
     * 新建TUN服务器环境
     *
     * @param tc    隧道上下文。
     * @return 新建的TUN服务器环境指针。
     */
    fun new_tun_server_env(tc: Long): Long

    /**
     * 启动TUN服务器
     *
     * @param env_ptr  TUN服务器环境指针。
     * @return 操作结果。
     */
    fun start_tun_server(env_ptr: Long): Pointer

    /**
     * 停止TUN服务器
     *
     * @param env_ptr  TUN服务器环境指针。
     */
    fun stop_tun_server(env_ptr: Long)
}
