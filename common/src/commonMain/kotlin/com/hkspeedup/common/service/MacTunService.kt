package com.hkspeedup.common.service

import cn.hutool.core.io.FileUtil
import cn.hutool.core.util.StrUtil
import com.hkspeedup.common.lib.FlyShadowCore
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.util.SystemUtil
import com.hkspeedup.common.viewmodel.ViewModel
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeoutOrNull
import kotlinx.coroutines.cancelAndJoin
import java.util.concurrent.TimeUnit

/**
 * macOS TUN服务实现
 */
object MacTunService : ITunService {

    private var viewModel: ViewModel? = null

    private var job: Job? = null

    private var tunProcess: Process? = null

    override fun openTun() {
        viewModel?.tunOpening = true
        try {
            if (!config.tunModelEnable) {
                GlobalLog.info("TUN model is disabled, skipping openTun")
                return
            }

            // 设置绑定本地网卡
            FlyShadowCore.setTunModeEnable(true)

            FlyShadowCore.newTunServerEnv()

            val openTunResult = FlyShadowCore.startTunServer()
            GlobalLog.info("OpenMacTun result: $openTunResult")
            if (StrUtil.isNotBlank(openTunResult)) {
                throw RuntimeException("Failed to open macOS TUN, result: $openTunResult")
            }

            startMacTun()
            GlobalLog.info("macOS TUN opened successfully")
        } catch (e: Exception) {
            GlobalLog.error(e, "Failed to open macOS TUN")
            throw e
        } finally {
            viewModel?.tunOpening = false
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    private fun startMacTun() {
        val arch = if (cn.hutool.system.SystemUtil.getOsInfo().arch == "aarch64") {
            "darwin-aarch64"
        } else {
            "darwin-x86-64"
        }
        this.javaClass.classLoader.getResourceAsStream("${arch}/flyshadow_mac_tun").use { it ->
            FileUtil.writeFromStream(it, "/tmp/flyshadow_mac_tun")
            job?.cancel()
            job = GlobalScope.launch(Dispatchers.IO) {
                try {
                    val chmodProcess = ProcessBuilder("chmod", "+x", "/tmp/flyshadow_mac_tun")
                        .start()
                    chmodProcess.waitFor()

                    val cmd = arrayOf(
                        "osascript", "-e",
                        "do shell script \"dscacheutil -flushcache; killall -HUP mDNSResponder; /tmp/flyshadow_mac_tun\" with administrator privileges"
                    )

                    tunProcess = ProcessBuilder(*cmd)
                        .redirectErrorStream(true)
                        .start()

                    val output = tunProcess!!.inputStream.bufferedReader().use { it.readText() }
                    GlobalLog.info("Mac Tun output: $output")

                    val exitCode = tunProcess!!.waitFor()
                    GlobalLog.info("Mac Tun process exited with code $exitCode")
                } catch (e: Exception) {
                    GlobalLog.error(e, "Start tun with sudo via osascript failed")
                } finally {
                    tunProcess = null
                    GlobalScope.launch(Dispatchers.Main) {
                        stopTun()
                    }
                }
            }
        }
    }

    override fun stopTun() {
        viewModel?.tunOpening = true
        try {
            GlobalLog.info("正在停止 macOS TUN...")

            // 禁用TUN模式并停止服务器
            FlyShadowCore.setTunModeEnable(false)
            FlyShadowCore.stopTunServer()

            // 强制停止任务和进程
            forceStopJob()

            // 杀死任何剩余的 flyshadow_mac_tun 进程
            killRemainingProcesses()

            ConfigService.save()
            GlobalLog.info("macOS TUN 停止成功")
        } catch (e: Exception) {
            GlobalLog.error(e, "停止 macOS TUN 失败")
        } finally {
            viewModel?.tunOpening = false
        }
    }

    /**
     * 强制停止协程任务和系统进程
     */
    private fun forceStopJob() {
        try {
            // 如果系统进程仍在运行，强制销毁它
            tunProcess?.let { process ->
                if (process.isAlive) {
                    GlobalLog.info("强制销毁 TUN 进程...")
                    process.destroyForcibly()
                    // 等待进程终止
                    val terminated = process.waitFor(3, TimeUnit.SECONDS)
                    if (terminated) {
                        GlobalLog.info("TUN 进程终止成功")
                    } else {
                        GlobalLog.info("TUN 进程在超时时间内未终止")
                    }
                }
                tunProcess = null
            }

            // 强制取消协程任务
            job?.let { currentJob ->
                GlobalLog.info("强制取消 TUN 任务...")
                runBlocking {
                    withTimeoutOrNull(5000) { // 5秒超时
                        currentJob.cancelAndJoin()
                        GlobalLog.info("TUN 任务取消成功")
                    } ?: run {
                        GlobalLog.info("TUN 任务取消超时")
                    }
                }
                job = null
            }
        } catch (e: Exception) {
            GlobalLog.error(e, "强制停止过程中发生错误")
        }
    }

    /**
     * 杀死任何剩余的 flyshadow_mac_tun 进程
     */
    private fun killRemainingProcesses() {
        try {
            GlobalLog.info("正在杀死剩余的 flyshadow_mac_tun 进程...")

            // 杀死任何剩余的 flyshadow_mac_tun 进程
            val killCmd = arrayOf(
                "osascript", "-e",
                "do shell script \"pkill -f flyshadow_mac_tun\" with administrator privileges"
            )
            val killProcess = ProcessBuilder(*killCmd)
                .redirectErrorStream(true)
                .start()

            val killExitCode = killProcess.waitFor()
            if (killExitCode == 0) {
                GlobalLog.info("成功杀死剩余的 flyshadow_mac_tun 进程")
            } else {
                GlobalLog.info("未找到剩余的 flyshadow_mac_tun 进程 (退出码: $killExitCode)")
            }
        } catch (e: Exception) {
            GlobalLog.error(e, "杀死剩余进程时发生错误")
        }
    }

    override fun setViewModel(viewModel: ViewModel) {
        this.viewModel = viewModel
    }
}