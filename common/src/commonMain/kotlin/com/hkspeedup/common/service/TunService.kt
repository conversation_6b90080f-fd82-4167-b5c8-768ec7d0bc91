package com.hkspeedup.common.service

import com.hkspeedup.common.util.SystemUtil
import com.hkspeedup.common.viewmodel.ViewModel

/**
 * TUN服务接口，提供统一的TUN操作抽象
 */
interface ITunService {
    /**
     * 打开TUN连接
     * @throws RuntimeException 当打开失败时抛出异常
     */
    fun openTun()
    
    /**
     * 停止TUN连接
     */
    fun stopTun()
    
    /**
     * 设置ViewModel
     */
    fun setViewModel(viewModel: ViewModel)
}

/**
 * TUN服务工厂，根据平台返回对应的TUN服务实现
 */
object TunServiceFactory {
    
    private var currentService: ITunService? = null
    
    /**
     * 获取当前平台的TUN服务实例
     * @return 对应平台的TUN服务实例
     * @throws UnsupportedOperationException 当平台不支持时抛出异常
     */
    @Synchronized
    fun getInstance(): ITunService {
        if (currentService == null) {
            currentService = when {
                SystemUtil.isWindows() -> WinTunService
                SystemUtil.isMac() -> MacTunService
                else -> throw UnsupportedOperationException("Unsupported platform for TUN service")
            }
        }
        return currentService!!
    }
    
    /**
     * 重置服务实例（主要用于测试）
     */
    internal fun reset() {
        currentService = null
    }
}

/**
 * TUN服务统一操作类，提供便捷的静态方法
 */
object TunService {
    
    private val service: ITunService by lazy { TunServiceFactory.getInstance() }
    
    /**
     * 打开TUN连接
     */
    fun openTun() {
        service.openTun()
    }
    
    /**
     * 停止TUN连接
     */
    fun stopTun() {
        service.stopTun()
    }
    
    /**
     * 设置ViewModel
     */
    fun setViewModel(viewModel: ViewModel) {
        service.setViewModel(viewModel)
    }
}
