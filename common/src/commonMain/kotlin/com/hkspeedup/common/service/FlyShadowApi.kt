package com.hkspeedup.common.service

import com.hkspeedup.common.vo.*
import retrofit2.http.*

interface FlyShadowApi {
    /**
     * 获取应用版本列表
     */
    @GET("/client/version/list")
    suspend fun getAppVersionList(): ResponseVo<List<AppVersion>>

    /**
     * 获取订阅内容
     */
    @GET("/subscribe")
    suspend fun getSubscribeContent(@Query(value = "password") password: String): SubscribeInfo

    /**
     * 获取自定义规则列表
     */
    @POST("/custom/rule/list")
    suspend fun getCustomRuleList(
        @Header(value = "subscribe-password") subscribePassword: String,
    ): ResponseVo<List<CustomRuleVo>>

    /**
     * 批量更新自定义规则
     */
    @POST("/custom/rule/updateList")
    suspend fun updateCustomRuleList(
        @Header(value = "subscribe-password") subscribePassword: String,
        @Body customRuleVos: List<CustomRuleVo>,
    ): ResponseVo<Void>

    /**
     * 保存自定义规则
     */
    @POST("/custom/rule/save")
    suspend fun saveCustomRule(
        @Header(value = "subscribe-password") subscribePassword: String,
        @Body customRuleVo: CustomRuleVo,
    ): ResponseVo<Void>

    /**
     * 自定义规则更新
     */
    @POST("/custom/rule/update")
    suspend fun updateCustomRule(
        @Header(value = "subscribe-password") subscribePassword: String,
        @Body customRuleVo: CustomRuleVo,
    ): ResponseVo<Void>

    /**
     * 删除自定义规则
     */
    @POST("/custom/rule/remove/{id}")
    suspend fun removeCustomRule(
        @Header(value = "subscribe-password") subscribePassword: String,
        @Path("id") id: Int,
    ): ResponseVo<Void>

    /**
     * 客户端信息上传
     */
    @POST("/client/saveOrUpdate")
    suspend fun clientUpload(
        @Header(value = "subscribe-password") subscribePassword: String,
        @Body clientVo: ClientVo,
    ): ResponseVo<Void>

    /**
     * 获取客户端列表
     */
    @GET("/client/list")
    suspend fun getClientList(
        @Header(value = "subscribe-password") subscribePassword: String,
    ): ResponseVo<List<ClientVo>>

    /**
     * 获取端口转发规则列表
     */
    @GET("/port/forwarding/list")
    suspend fun getPortForwardingList(
        @Header(value = "subscribe-password") subscribePassword: String,
    ): ResponseVo<List<PortForwardingVo>>

    /**
     * 端口转发规则保存或者更新
     */
    @POST("/port/forwarding/saveOrUpdate")
    suspend fun portForwardingSaveOrUpdate(
        @Header(value = "subscribe-password") subscribePassword: String,
        @Body portForwardingVo: PortForwardingVo,
    ): ResponseVo<Void>


    /**
     * 端口转发规则删除
     */
    @POST("/port/forwarding/delete/{id}")
    suspend fun deletePortForwarding(
        @Header(value = "subscribe-password") subscribePassword: String,
        @Path("id") id: Int,
    ): ResponseVo<Void>
}