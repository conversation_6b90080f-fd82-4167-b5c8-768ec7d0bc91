package com.hkspeedup.common.service

import cn.hutool.core.util.StrUtil
import cn.hutool.system.SystemUtil
import com.hkspeedup.common.lib.FlyShadowCore
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.viewmodel.ViewModel
import com.sun.jna.Native

/**
 * Windows TUN服务实现
 */
object WinTunService : ITunService {

    private var viewModel: ViewModel? = null

    init {
        // 只在Windows平台初始化WinTun环境
        if (SystemUtil.getOsInfo().isWindows) {
            try {
                val wintunDllFile = Native.extractFromResourcePath("wintun.dll")
                GlobalLog.info("Extract wintun lib to $wintunDllFile")
                FlyShadowCore.newWinTunEnv(wintunDllFile.absolutePath)
                GlobalLog.info("New win tun env success")
            } catch (e: Exception) {
                GlobalLog.error(e, "Failed to initialize WinTun environment")
                throw RuntimeException("Failed to initialize WinTun environment", e)
            }
        }
    }

    override fun openTun() {
        viewModel?.tunOpening = true
        try {
            if (!config.tunModelEnable) {
                GlobalLog.info("TUN model is disabled, skipping openTun")
                return
            }

            // 设置绑定本地网卡
            FlyShadowCore.setTunModeEnable(true)

            val openTunResult = FlyShadowCore.openWinTun()
            GlobalLog.info("OpenWinTun result: $openTunResult")
            if (StrUtil.isNotBlank(openTunResult)) {
                throw RuntimeException("Failed to open Windows TUN, result: $openTunResult")
            }

            val startTunExchangeResult = FlyShadowCore.startWinTunExchange()
            GlobalLog.info("StartWinTunExchange result: $startTunExchangeResult")
            if (StrUtil.isNotBlank(startTunExchangeResult)) {
                throw RuntimeException("Failed to start Windows TUN data exchange, result: $startTunExchangeResult")
            }

            GlobalLog.info("Windows TUN opened successfully")
        } catch (e: Exception) {
            GlobalLog.error(e, "Failed to open Windows TUN")
            throw e
        } finally {
            viewModel?.tunOpening = false
        }
    }

    override fun stopTun() {
        viewModel?.tunOpening = true
        try {
            FlyShadowCore.setTunModeEnable(false)
            FlyShadowCore.stopWinTun()
            GlobalLog.info("Windows TUN stopped successfully")
        } catch (e: Exception) {
            GlobalLog.error(e, "Failed to stop Windows TUN")
        } finally {
            viewModel?.tunOpening = false
        }
    }

    override fun setViewModel(viewModel: ViewModel) {
        this.viewModel = viewModel
    }
}

