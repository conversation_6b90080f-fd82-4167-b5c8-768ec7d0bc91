package com.hkspeedup.common.service

import cn.hutool.core.bean.copier.CopyOptions
import cn.hutool.core.util.StrUtil
import com.hkspeedup.common.lib.FlyShadowCore
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.util.GsonUtil
import com.hkspeedup.common.util.getConfigFilePath
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.viewmodel.ViewModel
import com.hkspeedup.common.vo.*
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.saveConfigFail
import kotlinx.coroutines.*
import org.modelmapper.ModelMapper
import java.io.*
import java.util.*
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid


val config = Config()

data class Config @OptIn(ExperimentalUuidApi::class) constructor(
    var currentLanguage: String? = null, // 当前语言环境
    var subscribePassword: String = "", // 订阅密码
    var subscribeInfo: SubscribeInfo? = null, // 订阅信息
    var selectNodeName: String? = null, // 选择的节点索引
    var proxyPort: Int = 6785, // 代理端口
    var lastUpdateTime: Date? = null, // 最后更新时间
    var nativeUdp: Boolean = false, // 原生UDP
    var directConnPriority: Boolean = true, // 直连优先
    var directConnPriorityTimeout: Long = 180, // 直连优先超时时间
    var launchOnStartUp: Boolean = false, // 开机后启动
    var minimizeAfterStartUp: Boolean = false, // 启动后最小化
    var autoSetSysProxyOnStartUp: Boolean = true,
    var apps: HashSet<String> = hashSetOf(), // 应用列表
    var excludeAppsMode: Int = 0, // 0排除选中 1允许选中应用 2允许所有应用
    var proxyType: Int = 2, //代理模式 0直连 1全局 2规则
    var udpProxyType: Int = 0, // UDP代理模式 0直连 1全局 2规则
    var tunModelEnable: Boolean = false, // TUN模式
    var ipv6Enable: Boolean = false, // IPv6启用
    var useBuildInDns: Boolean = true, // IPv6启用
    var startUpAsAdmin: Boolean = true, // 以管理员开启启动
    var customRuleList: List<CustomRuleVo> = arrayListOf(), // 自定义规则列表
    var delaySortType: Int = 0, // 延迟排序
    var starNodeNameList: HashSet<String> = hashSetOf(), // 收藏的节点名字
    var starNodeCnNameList: HashSet<String> = hashSetOf(), // 收藏的节点中文名字
    var uuid: String = Uuid.random().toHexString(), // 客户端ID
    var portForwardingList: List<PortForwardingVo> = arrayListOf(), // 端口转发列表
    var darkMode: Boolean = false, // 深色模式
) {
    fun getSelectNodeInfo(): NodeInfo? {
        val firstOrNull = config.subscribeInfo?.node?.firstOrNull {
            if (config.currentLanguage == "ZH") {
                it.cnName
            } else {
                it.name
            } == config.selectNodeName
        }
        if (firstOrNull == null) {
            config.selectNodeName = null
        }
        return firstOrNull
    }
}

fun readFileToString(file: File?): String {
    val stringBuilder = StringBuilder()

    val bufferedReader = BufferedReader(FileReader(file))
    var line: String?

    while ((bufferedReader.readLine().also { line = it }) != null) {
        stringBuilder.append(line)
        stringBuilder.append("\n") // 如果需要保留换行符，可以将其添加到字符串中
    }

    bufferedReader.close()


    return stringBuilder.toString()
}

fun writeStringToFile(content: String?, file: File) {
    val bufferedWriter = BufferedWriter(FileWriter(file))
    bufferedWriter.write(content)
    bufferedWriter.close()
}

fun deleteFile(filePath: String) {
    val file = File(filePath)

    if (file.exists()) {
        file.delete()
    }
}

// 配置服务类，供其他类调用用于完成配置相关操作
object ConfigService {
    // 保存配置信息到用户主目录下的特定文件
    @OptIn(DelicateCoroutinesApi::class)
    fun save() {
        GlobalLog.info("Save config")
        GlobalScope.launch(Dispatchers.IO) {
            try {
                mkdir()
                val userHomeDir = getConfigFilePath()
                val jsonStr = GsonUtil.createGson().toJson(config)
                writeStringToFile(jsonStr, File("$userHomeDir/.FlyShadow/config.json"))
                loadDomainRuleList()
            } catch (e: Exception) {
                GlobalLog.error(e, "保存配置文件失败")
                ViewFunction.showAlertDialog(AlertDialogMessage(StringContentVo(Res.string.saveConfigFail, e.message)))
            }
        }
    }

    // 从用户主目录下的特定文件读取配置信息
    fun load(viewModel: ViewModel) {
        GlobalLog.info("Load config")
        val userHomeDir = getConfigFilePath()
        val configPath = "$userHomeDir/.FlyShadow/config.json"
        try {
            mkdir()
            val json = readFileToString(File(configPath))
            val config1 = GsonUtil.createGson().fromJson(json, Config::class.java)
            val copyOptions = CopyOptions.create()
            copyOptions.setTransientSupport(false)
            copyOptions.ignoreNullValue()
            ModelMapper().map(config1, config)
            config.subscribeInfo?.node?.let { it ->
                if (it.find {
                        if (config.currentLanguage == "ZH") {
                            it.cnName
                        } else {
                            it.name
                        } == config.selectNodeName
                    } == null) {
                    config.selectNodeName = null
                }
            }
            setViewModel(viewModel)
        } catch (e: Exception) {
            GlobalLog.error(e, "读取配置文件失败")
            deleteFile(configPath)
        }
    }

    fun setViewModel(viewModel: ViewModel) {
        viewModel.tunEnable = config.tunModelEnable
        viewModel.darkMode = config.darkMode
        if (StrUtil.isNotBlank(config.currentLanguage)) {
            Locale.setDefault(Locale(config.currentLanguage))
        } else {
            if ("zh".equals(Locale.getDefault().language, true)) {
                config.currentLanguage = "ZH"
            } else {
                config.currentLanguage = "EN"
            }
        }
        viewModel.language = config.currentLanguage!!
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun loadLaunchOnStartup() {
        if (config.launchOnStartUp) {
            GlobalScope.launch(Dispatchers.IO) {
                ViewFunction.setProxyStatus(ProxyStatus.CONNECTING)
                delay(2000)
                runCatching {
                    ProxyService.startTunnel()
                }
            }
        }
    }

    /**
     * 加载域名规则列表
     */
    fun loadDomainRuleList() {
        FlyShadowCore.clearDomainRule()
        config.customRuleList.filter { it.enable == 1 }.forEach {
            FlyShadowCore.setDomainRuleObj(it.domain, it.matching, it.proxyType, it.directConnPriority)
        }
        config.subscribeInfo?.customRule?.forEach {
            FlyShadowCore.setDomainRuleObj(it.domain!!, it.matching!!, it.proxyType!!, it.directConnPriority)
        }
    }

    // 如果不存在应用的配置目录，创建它
    private fun mkdir() {
        val userHomeDir = getConfigFilePath()

        val logFolder = File("$userHomeDir/.FlyShadow")
        if (!logFolder.exists()) {
            logFolder.mkdirs()
        }
    }

}