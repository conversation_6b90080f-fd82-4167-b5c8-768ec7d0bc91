package com.hkspeedup.common.service

expect object ProxyService {


    // 开始代理的函数，使用协程进行异步操作
    fun startProxy(callback: (message: String?) -> Unit)

    // 停止代理的函数
    fun stopProxy()

    // 测试隧道时延函数
    // 完成之后的回调函数
    fun testTunnelDelay(finish: (Long, String) -> Unit)

    // 开启隧道函数
    fun startTunnel()

    // 停止隧道函数
    fun stopTunnel(stopFinish: () -> Unit)

    // 重新选择隧道连接
    fun reSelectTunnel()

    // 重启隧道函数
    fun restartTunnel()
}