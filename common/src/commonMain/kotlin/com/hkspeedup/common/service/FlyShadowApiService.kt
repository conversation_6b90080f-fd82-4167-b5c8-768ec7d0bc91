package com.hkspeedup.common.service

import com.hkspeedup.common.util.GsonUtil
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.ResponseBody.Companion.toResponseBody
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.net.InetSocketAddress
import java.net.Proxy
import java.util.concurrent.TimeUnit

const val HTTPS_API_FLYSHADOW_URL = "https://main.hkspeedup.com"

object FlyShadowApiService {

    private var okHttpClient: OkHttpClient? = null
    private var useProxy = false

    private val retryInterceptor = Interceptor { chain ->
        val request = chain.request()
        var response: okhttp3.Response? = null
        var exception: Exception? = null

        // 第一次尝试
        try {
            response = chain.proceed(request)
            // 检查响应是否成功
            if (response.isSuccessful) {
                return@Interceptor response
            } else {
                // HTTP错误，准备重试
                response.close()
                exception = Exception("HTTP error: ${response.code}")
            }
        } catch (e: Exception) {
            // 网络错误或其他异常，准备重试
            exception = e
        }

        // 等待10秒后重试
        runBlocking {
            delay(10000)
        }

        // 第二次尝试（重试）
        try {
            response = chain.proceed(request)
            return@Interceptor response
        } catch (e: Exception) {
            // 重试也失败，抛出原始异常
            throw exception
        }
    }

    private val loggingInterceptor = Interceptor { chain ->
        val request = chain.request()

        println("Sending request to URL: ${request.url}")
        println("Method: ${request.method}")
        println("Headers: ${request.headers}")

        // 打印请求体（如果是文本）
        request.body?.let { body ->
            try {
                val buffer = okio.Buffer()
                body.writeTo(buffer)
                val charset = body.contentType()?.charset(Charsets.UTF_8) ?: Charsets.UTF_8
                println("Request Body: ${buffer.readString(charset)}")
            } catch (e: Exception) {
                println("Failed to read request body: ${e.message}")
            }
        }

        val response = chain.proceed(request)

        val responseBody = response.body
        val content = try {
            responseBody?.source()?.apply { request(Long.MAX_VALUE) }
                ?.buffer
                ?.clone()
                ?.readUtf8()
        } catch (e: Exception) {
            "Failed to read response body: ${e.message}"
        }

        println("Response Code: ${response.code}")
        println("Response Body: $content")

        // 重建响应体
        val newResponseBody = responseBody?.contentType()?.let { contentType ->
            content?.toByteArray()?.toResponseBody(contentType)
        }

        response.newBuilder()
            .body(newResponseBody ?: response.body)
            .build()
    }

    private fun createOkHttpClient(useProxy: Boolean): OkHttpClient {
        val builder = OkHttpClient.Builder()

        if (useProxy) {
            val proxy = Proxy(Proxy.Type.HTTP, InetSocketAddress("127.0.0.1", config.proxyPort))
            builder.proxy(proxy)
        }

        // 添加重试拦截器（在日志拦截器之前，确保重试逻辑先执行）
        builder.addInterceptor(retryInterceptor)
        builder.addInterceptor(loggingInterceptor)
        builder.connectTimeout(90, TimeUnit.SECONDS)
        builder.readTimeout(90, TimeUnit.SECONDS)
        builder.writeTimeout(90, TimeUnit.SECONDS)


        return builder.build()
    }

    fun createApi(useProxy: Boolean): FlyShadowApi {
        if (okHttpClient == null) {
            okHttpClient = createOkHttpClient(useProxy)
            this.useProxy = useProxy
        }

        if (this.useProxy != useProxy) {
            okHttpClient = createOkHttpClient(useProxy)
            this.useProxy = useProxy
        }

        return Retrofit.Builder()
            .baseUrl(HTTPS_API_FLYSHADOW_URL)
            .client(okHttpClient!!)
            .addConverterFactory(GsonConverterFactory.create(GsonUtil.createGson()))
            .build()
            .create(FlyShadowApi::class.java)
    }
}