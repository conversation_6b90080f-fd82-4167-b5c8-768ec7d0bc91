package com.hkspeedup.common.service

import com.hkspeedup.common.lib.FlyShadowCore
import com.hkspeedup.common.util.Base64Util
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewModel
import com.hkspeedup.common.vo.ClientVo
import jdk.internal.org.jline.utils.Colors.s
import kotlinx.coroutines.delay
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

object PortForwardingService {

    suspend fun init(subscribePassword: String) {
        config.portForwardingList.filter { it.enable && it.uuid == config.uuid }.forEach {
            it.errorMessage = FlyShadowCore.addPortForwarding(it.listenPort, it.targetAddr, it.targetUuid)
        }
        kotlin.runCatching {
            uploadUuid(subscribePassword, null)
            getPortForwardingList(subscribePassword, null)
        }
    }

    suspend fun getClientList(
        subscribePassword: String,
        viewModel: ViewModel?
    ): List<ClientVo>? {
        return FlyShadowApiService.createApi(viewModel?.proxyStatus == ProxyStatus.START)
            .getClientList(Base64Util.encode(subscribePassword))
            .data
    }

    suspend fun getPortForwardingList(
        subscribePassword: String,
        viewModel: ViewModel?
    ) {
        uploadUuid(subscribePassword, viewModel)
        FlyShadowApiService.createApi(viewModel?.proxyStatus == ProxyStatus.START)
            .getPortForwardingList(Base64Util.encode(subscribePassword))
            .data?.let { it ->
                config.portForwardingList = it
                ConfigService.save()
                FlyShadowCore.clearPortForwarding()
                config.portForwardingList.filter { it.enable && it.uuid == config.uuid }.forEach {
                    it.errorMessage = FlyShadowCore.addPortForwarding(it.listenPort, it.targetAddr, it.targetUuid)
                }
            }
    }

    @OptIn(ExperimentalUuidApi::class)
    suspend fun resetUuid(
        subscribePassword: String,
        viewModel: ViewModel?
    ) {
        config.uuid = Uuid.random().toHexString()
        uploadUuid(subscribePassword, viewModel)
    }

    private suspend fun uploadUuid(
        subscribePassword: String,
        viewModel: ViewModel?
    ) {
        FlyShadowCore.setClientUuid(config.uuid)
        ConfigService.save()
        FlyShadowApiService.createApi(viewModel?.proxyStatus == ProxyStatus.START)
            .clientUpload(Base64Util.encode(subscribePassword), ClientVo(uuid = config.uuid, null))
    }
}