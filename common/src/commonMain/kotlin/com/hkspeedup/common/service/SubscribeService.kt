package com.hkspeedup.common.service

import cn.hutool.core.util.StrUtil
import com.hkspeedup.common.util.Base64Util
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.util.GsonUtil
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewModel
import com.hkspeedup.common.vo.StringContentVo
import com.hkspeedup.common.vo.SubscribeInfo
import flyshadow.common.generated.resources.*
import kotlinx.coroutines.*
import java.io.File
import java.net.UnknownHostException
import java.util.*

@OptIn(DelicateCoroutinesApi::class)
object SubscribeService {

    init {
        GlobalScope.launch(Dispatchers.IO) {
            while (true) {
                // 6小时更新一次
                delay(6 * 60 * 60 * 1000L)
                if (StrUtil.isNotBlank(config.subscribePassword)) {
                    kotlin.runCatching { setSubscribeUrl(config.subscribePassword) }
                        .onSuccess {
                            GlobalLog.info("更新订阅成功")
                        }
                        .onFailure {
                            GlobalLog.error(it, "更新订阅失败")
                        }
                }
            }
        }
    }

    var updateSubscribeCallback: (stringContentVo: StringContentVo?) -> Unit = {}

    fun setSubscribeUrl(
        subscribePassword: String,
    ) {
        setSubscribeUrl(subscribePassword, null, false)
    }

    fun setSubscribeUrl(
        subscribePassword: String,
        viewModel: ViewModel?,
    ) {
        setSubscribeUrl(subscribePassword, viewModel, true)
    }

    // 从文件导入订阅信息
    @OptIn(DelicateCoroutinesApi::class)
    fun importSubscribeFromFile(
        filePath: String,
    ) {
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val file = File(filePath)
                if (!file.exists()) {
                    updateSubscribeCallback(StringContentVo(Res.string.fileImportError, "File not found"))
                    return@launch
                }

                val jsonContent = file.readText(Charsets.UTF_8)
                importSubscribeFromJsonContent(jsonContent)

            } catch (e: Exception) {
                GlobalLog.error(e, "Import subscribe from file error: $filePath")
                updateSubscribeCallback(StringContentVo(Res.string.fileImportError, e.message ?: "Unknown error"))
            }
        }
    }

    // 从JSON内容导入订阅信息（用于Android文件选择器）
    @OptIn(DelicateCoroutinesApi::class)
    fun importSubscribeFromJsonContent(
        jsonContent: String,
    ) {
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val subscribeInfo = GsonUtil.createGson().fromJson(jsonContent, SubscribeInfo::class.java)

                // 验证JSON格式是否正确
                if (subscribeInfo == null) {
                    updateSubscribeCallback(StringContentVo(Res.string.invalidJsonFile, ""))
                    return@launch
                }

                config.subscribeInfo = subscribeInfo
                config.lastUpdateTime = Date()

                // 检查当前选择的节点是否还存在
                config.subscribeInfo?.node?.let { nodeList ->
                    if (nodeList.find {
                            if (config.currentLanguage == "ZH") {
                                it.cnName
                            } else {
                                it.name
                            } == config.selectNodeName
                        } == null) {
                        config.selectNodeName = null
                    }
                }

                GlobalLog.info("Import Subscribe Info Success from JSON content, NodeCount: ${subscribeInfo.node?.size}")
                ConfigService.save()
                updateSubscribeCallback(null)

            } catch (e: Exception) {
                GlobalLog.error(e, "Import subscribe from JSON content error")
                updateSubscribeCallback(StringContentVo(Res.string.fileImportError, e.message ?: "Unknown error"))
            }
        }
    }

    // 设置订阅连接
    @OptIn(DelicateCoroutinesApi::class)
    private fun setSubscribeUrl(
        subscribePassword: String,
        viewModel: ViewModel?,
        showMessageNotification: Boolean,
    ) =
        GlobalScope.launch(Dispatchers.IO) {
            val body: String? = null
            try {
                var subscribeInfo: SubscribeInfo
                if (StrUtil.isNotBlank(subscribePassword)) {
                        subscribeInfo = FlyShadowApiService.createApi(viewModel?.proxyStatus == ProxyStatus.START)
                            .getSubscribeContent(subscribePassword)
                        val customRuleList =
                            FlyShadowApiService.createApi(viewModel?.proxyStatus == ProxyStatus.START).getCustomRuleList(
                                Base64Util.encode(subscribePassword)
                            )
                        customRuleList.data?.let {
                            config.customRuleList = customRuleList.data
                        }
                        PortForwardingService.getPortForwardingList(subscribePassword, viewModel)
                } else {
                    if (showMessageNotification) {
                        updateSubscribeCallback(StringContentVo(Res.string.passwordCannotBeEmpty, ""))
                    }
                    return@launch
                }

                config.subscribePassword = subscribePassword
                config.subscribeInfo = subscribeInfo
                config.lastUpdateTime = Date()
                config.subscribeInfo?.node?.let { it ->
                    if (it.find {
                            if (config.currentLanguage == "ZH") {
                                it.cnName
                            } else {
                                it.name
                            } == config.selectNodeName
                        } == null) {
                        config.selectNodeName = null
                    }
                }
                GlobalLog.info("Get Subscribe Info Success, NodeCount: ${subscribeInfo.node?.size}")
                ConfigService.save()
                if (showMessageNotification) {
                    updateSubscribeCallback(null)
                }
            } catch (e: UnknownHostException) {
                GlobalLog.error(e, "获取订阅错误")
                if (showMessageNotification) {
                    updateSubscribeCallback(StringContentVo(Res.string.unknownHostException, ""))
                }
            } catch (e: Exception) {
                val bodySplit = com.hkspeedup.common.util.StrUtil.maxLength(body, 300) ?: ""
                GlobalLog.error(e, "获取订阅错误: $body")
                if (showMessageNotification) {
                    updateSubscribeCallback(StringContentVo(Res.string.error, "${e.message} \n $bodySplit"))
                }
            }
        }

}