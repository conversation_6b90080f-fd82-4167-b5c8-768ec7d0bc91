package com.hkspeedup.common.ui.component

import androidx.compose.foundation.layout.*
import androidx.compose.material.LocalContentColor
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.viewmodel.ViewFunction

@Composable
fun BackNavBar(title: String, backCallback: () -> Boolean = { true }, content: @Composable RowScope.() -> Unit = {}) {
    Row(
        modifier = Modifier.fillMaxWidth().height(35.dp).padding(horizontal = 5.dp).enableFocusable(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        CircularButton(
            imageVector = IconCustomArrowBack,
            onClick = {
                if (backCallback()) {
                    ViewFunction.backNavigationStackPage()
                }
            }
        )
        Column(Modifier.weight(1f)) {
            Text(
                title,
                fontSize = 13.sp,
                lineHeight = 1.sp,
                color = MaterialTheme.colors.onSurface,
                modifier = Modifier.padding(start = 5.dp)
            )
        }
        CompositionLocalProvider(LocalContentColor provides MaterialTheme.colors.onSurface) {
            content()
        }
    }
}