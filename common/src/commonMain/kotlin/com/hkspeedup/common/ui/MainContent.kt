package com.hkspeedup.common.ui

import androidx.compose.animation.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material.icons.filled.Settings
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.listSaver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.util.VersionChecker
import com.hkspeedup.common.viewmodel.*
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.server
import flyshadow.common.generated.resources.setting
import kotlinx.coroutines.*
import org.jetbrains.compose.resources.StringResource
import org.jetbrains.compose.resources.stringResource

enum class BottomMenu(val title: StringResource, val icon: ImageVector) {
    NODES(Res.string.server, Icons.AutoMirrored.Filled.List),
    SETTINGS(Res.string.setting, Icons.Filled.Settings)
}

@OptIn(ExperimentalAnimationApi::class)
@Composable
fun InitMainContent(viewModel: ViewModel) {
    var selectedMenu by remember { mutableStateOf(BottomMenu.NODES) }
    ViewFunction.setProxyStatus = { viewModel.proxyStatus = it }
    viewModel.language

    val navigationStack = rememberSaveable(
        saver = listSaver<NavigationStack<Page>, Page>(
            restore = { NavigationStack(*it.toTypedArray()) },
            save = { it.stack },
        )
    ) {
        NavigationStack(MainPage())
    }

    ViewFunction.pushNavigationStackPage = {
        navigationStack.push(it)
    }

    ViewFunction.backNavigationStackPage = {
        navigationStack.back()
    }


    VersionCheck(viewModel)

    val bgColorBrush = Brush.linearGradient(
        colors = listOf(
            MaterialTheme.colors.primary.copy(alpha = 0.75f),
            MaterialTheme.colors.background.copy(alpha = 0.75f),
            MaterialTheme.colors.secondary.copy(alpha = 0.75f),
        ),
        start = Offset(0f, 0f),  // 顶部
        end = Offset(0f, Float.POSITIVE_INFINITY)  // 底部
    )

    AnimatedContent(targetState = navigationStack.lastWithIndex(), transitionSpec = {
        val previousIdx = initialState.index
        val currentIdx = targetState.index
        val multiplier = if (previousIdx < currentIdx) 1 else -1

        slideInHorizontally { w -> multiplier * w } with
                slideOutHorizontally { w -> multiplier * -1 * w }
    }) { (_, page) ->
        when (page) {
            is MainPage -> {
                Scaffold(
                    bottomBar = {
                        BottomAppBar(contentPadding = PaddingValues(0.dp)) {
                            BottomNavigation {
                                BottomMenu.entries.forEach { menu ->
                                    BottomNavigationItem(
                                        modifier = Modifier.background(MaterialTheme.colors.background).enableFocusable(),
                                        selected = selectedMenu == menu,
                                        onClick = {
                                            selectedMenu = menu
                                        },
                                        icon = {
                                            Icon(
                                                menu.icon,
                                                contentDescription = stringResource(menu.title),
                                                tint = if (selectedMenu == menu) MaterialTheme.colors.primary else MaterialTheme.colors.onSurface,
                                                modifier = Modifier.size(22.dp)
                                            )
                                            if (menu == BottomMenu.SETTINGS) {
                                                viewModel.version?.let {
                                                    val errorColor = MaterialTheme.colors.error
                                                    Canvas(modifier = Modifier.size(3.dp)) {
                                                        drawCircle(
                                                            color = errorColor,
                                                            radius = 3.dp.toPx(),  // Size of the circle
                                                            center = this.center  // Center of the canvas
                                                        )
                                                    }
                                                }
                                            }
                                        },
                                        label = {
                                            Text(
                                                stringResource(menu.title),
                                                color = if (selectedMenu == menu) MaterialTheme.colors.primary else MaterialTheme.colors.onSurface,
                                                fontSize = 11.sp,
                                                lineHeight = 1.sp
                                            )
                                        }
                                    )
                                }
                            }
                        }
                    }
                ) {
                    Box(modifier = Modifier.fillMaxSize().background(brush = bgColorBrush)) {
                        when (selectedMenu) {
                            BottomMenu.NODES -> NodesPage(viewModel)
                            BottomMenu.SETTINGS -> SettingPage(viewModel)
                        }
                    }
                }
            }

            is TunnelMapperInfoPage -> {
                Box(Modifier.fillMaxSize().background(brush = bgColorBrush)) {
                    TunnelMapperInfoView()
                }
            }

            is PortForwardingListPage -> {
                Box(Modifier.fillMaxSize().background(brush = bgColorBrush)) {
                    PortForwardingListInfoView(viewModel)
                }
            }

            is PortForwardingDetailPage -> {
                Box(Modifier.fillMaxSize().background(brush = bgColorBrush)) {
                    PortForwardingDetailView(viewModel)
                }
            }

            is PortForwardingRemarkPage -> {
                Box(Modifier.fillMaxSize().background(brush = bgColorBrush)) {
                    PortForwardingRemarkView(viewModel)
                }
            }

            is VersionInfoPage -> {
                Box(Modifier.fillMaxSize().background(brush = bgColorBrush)) {
                    VersionInfoView(viewModel)
                }
            }

            is SetSubscribeSettingPage -> {
                Box(Modifier.fillMaxSize().background(brush = bgColorBrush)) {
                    SetSubscribeLinkView(viewModel)
                }
            }

            is AppInfoPage -> {
                Box(Modifier.fillMaxSize().background(brush = bgColorBrush)) {
                    AppInfo(viewModel)
                }
            }

            is LocalProxyPortPage -> {
                Box(Modifier.fillMaxSize().background(brush = bgColorBrush)) {
                    LocalProxyPortView(viewModel)
                }
            }

            is CustomRuleListPage -> {
                Box(Modifier.fillMaxSize().background(brush = bgColorBrush)) {
                    CustomRuleListView(viewModel)
                }
            }

            is CustomRuleDetailPage -> {
                Box(Modifier.fillMaxSize().background(brush = bgColorBrush)) {
                    CustomRuleDetailView(viewModel)
                }
            }
        }
    }
}

@OptIn(DelicateCoroutinesApi::class)
@Composable
fun VersionCheck(viewModel: ViewModel) {
    DisposableEffect(Unit) {
        val job = GlobalScope.launch(Dispatchers.IO) {
            while (true) {
                VersionChecker.checkForUpdate(viewModel)
                delay(60 * 60 * 1000)
            }
        }
        // 使用 DisposableEffect 来清理资源或取消工作
        onDispose {
            job.cancel() // 在界面消失时取消协程
        }
    }
}