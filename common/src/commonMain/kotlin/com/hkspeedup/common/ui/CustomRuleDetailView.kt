package com.hkspeedup.common.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Switch
import androidx.compose.material.SwitchDefaults
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.hkspeedup.common.ui.theme.appSwitchColors
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.hutool.core.util.StrUtil
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.service.FlyShadowApiService
import com.hkspeedup.common.service.config
import com.hkspeedup.common.ui.component.BackNavBar
import com.hkspeedup.common.ui.component.CustomTextField
import com.hkspeedup.common.ui.component.DropdownMenu
import com.hkspeedup.common.util.Base64Util
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.viewmodel.ViewModel
import com.hkspeedup.common.vo.CustomRuleVo
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.*
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import java.util.*

object CustomRuleDetailView {
    var customRuleVo: CustomRuleVo? = null
}

@OptIn(DelicateCoroutinesApi::class)
@Composable
fun CustomRuleDetailView(viewModel: ViewModel) {

    var domain by remember { mutableStateOf(TextFieldValue(CustomRuleDetailView.customRuleVo?.domain ?: "")) }
    var matching by remember { mutableStateOf(CustomRuleDetailView.customRuleVo?.matching ?: 0) }
    var proxyType by remember { mutableStateOf(CustomRuleDetailView.customRuleVo?.proxyType ?: 0) }
    var enable by remember { mutableStateOf(CustomRuleDetailView.customRuleVo?.enable ?: 1) }
    var directConnPriority by remember { mutableStateOf(CustomRuleDetailView.customRuleVo?.directConnPriority ?: 0) }

    val matchingRules = listOf(
        stringResource(Res.string.domainNameMatch),
        stringResource(Res.string.domainNameSuffixMatch),
        stringResource(Res.string.domainNameKeywordMatch),
        stringResource(Res.string.CIDR4Match),
        stringResource(Res.string.CIDR6Match),
        stringResource(Res.string.srcCIDR4Match),
        stringResource(Res.string.geoIpMatch),
        stringResource(Res.string.dstPortMatch),
        stringResource(Res.string.srcPortMatch),
        stringResource(Res.string.processNameMatch),
        stringResource(Res.string.allMatch),
        stringResource(Res.string.clashClassicalMatch),
        stringResource(Res.string.clashDomainMatch),
    )
    val proxyRules = listOf(
        stringResource(Res.string.direct),
        stringResource(Res.string.reject),
        stringResource(Res.string.proxy),
    )

    Box(
        Modifier.padding(10.dp)
    ) {
        // 编辑自定义规则标题
        Column {
            Row(Modifier.background(MaterialTheme.colors.surface, shape = RoundedCornerShape(10.dp))) {
                BackNavBar(stringResource(Res.string.customRuleList)) {
                    CustomRuleDetailView.customRuleVo?.id?.let {
                        TextButton(onClick = {
                            GlobalScope.launch(Dispatchers.IO) {
                                ViewFunction.setWindowLoading(true)
                                kotlin.runCatching {
                                    if (StrUtil.isNotBlank(config.subscribePassword)) {
                                        val removeCustomRule =
                                            FlyShadowApiService.createApi(viewModel.proxyStatus == ProxyStatus.START)
                                                .removeCustomRule(
                                                    Base64Util.encode(config.subscribePassword),
                                                    it
                                                )
                                        if (removeCustomRule.code == 200) {
                                            ViewFunction.backNavigationStackPage()
                                        } else {
                                            ViewFunction.showAlertDialog(
                                                AlertDialogMessage(
                                                    StringContentVo(
                                                        null,
                                                        removeCustomRule.message
                                                    )
                                                )
                                            )
                                        }
                                    }
                                }.onFailure {
                                    GlobalLog.error(it, "删除自定义规则列表失败")
                                    if (it.message?.contains("401") == true) {
                                        ViewFunction.showAlertDialog(
                                            AlertDialogMessage(
                                                StringContentVo(
                                                    Res.string.loginFailure,
                                                    null
                                                )
                                            )
                                        )
                                    }
                                }
                                ViewFunction.setWindowLoading(false)
                            }
                        }, modifier = Modifier.enableFocusable()) {
                            Text(stringResource(Res.string.delete))
                        }

                    }


                    TextButton(onClick = {
                        GlobalScope.launch(Dispatchers.IO) {
                            ViewFunction.setWindowLoading(true)
                            kotlin.runCatching {
                                if (CustomRuleDetailView.customRuleVo?.id != null) {
                                    val updateCustomRule =
                                        FlyShadowApiService.createApi(viewModel.proxyStatus == ProxyStatus.START)
                                            .updateCustomRule(
                                                Base64Util.encode(config.subscribePassword),
                                                CustomRuleVo(
                                                    CustomRuleDetailView.customRuleVo?.id,
                                                    enable,
                                                    domain.text,
                                                    matching,
                                                    proxyType,
                                                    directConnPriority,
                                                    null,
                                                    CustomRuleDetailView.customRuleVo?.sort ?: -1
                                                )
                                            )
                                    if (updateCustomRule.code == 200) {
                                        ViewFunction.backNavigationStackPage()
                                    } else {
                                        ViewFunction.showAlertDialog(
                                            AlertDialogMessage(
                                                StringContentVo(
                                                    null,
                                                    updateCustomRule.message
                                                )
                                            )
                                        )
                                    }
                                } else {
                                    val saveCustomRule =
                                        FlyShadowApiService.createApi(viewModel.proxyStatus == ProxyStatus.START)
                                            .saveCustomRule(
                                                Base64Util.encode(config.subscribePassword),
                                                CustomRuleVo(
                                                    CustomRuleDetailView.customRuleVo?.id,
                                                    enable,
                                                    domain.text,
                                                    matching,
                                                    proxyType,
                                                    directConnPriority,
                                                    Date(),
                                                    -1
                                                )
                                            )
                                    if (saveCustomRule.code == 200) {
                                        ViewFunction.backNavigationStackPage()
                                    } else {
                                        ViewFunction.showAlertDialog(
                                            AlertDialogMessage(
                                                StringContentVo(
                                                    null,
                                                    saveCustomRule.message
                                                )
                                            )
                                        )
                                    }
                                }
                            }.onFailure {
                                GlobalLog.error(it, "更新自定义规则列表失败")
                                if (it.message?.contains("401") == true) {
                                    ViewFunction.showAlertDialog(
                                        AlertDialogMessage(
                                            StringContentVo(
                                                Res.string.loginFailure,
                                                null
                                            )
                                        )
                                    )
                                }
                            }
                            ViewFunction.setWindowLoading(false)
                        }
                    }, modifier = Modifier.enableFocusable()) {
                        Text(stringResource(Res.string.save))
                    }
                }
            }

            // 输入域名
            Text(
                stringResource(Res.string.valueMatching),
                modifier = Modifier.padding(top = 10.dp, bottom = 5.dp),
                fontSize = 11.sp,
                color = MaterialTheme.colors.onSurface
            )
            Row(Modifier.background(MaterialTheme.colors.surface, shape = RoundedCornerShape(5.dp))) {
                CustomTextField(
                    value = domain,
                    onValueChange = { domain = it },
                    modifier = Modifier
                        .fillMaxWidth()
                        .enableFocusable()
                        .padding(horizontal = 15.dp, vertical = 5.dp)
                        .background(MaterialTheme.colors.surface)
                        .height(30.dp),
                    singleLine = true,
                    textStyle = TextStyle(fontSize = 12.sp),
                    contentPadding = PaddingValues(5.dp)
                )
            }


            Spacer(modifier = Modifier.padding(10.dp))
            // 选择匹配规则
            Text(
                stringResource(Res.string.matchingRule),
                modifier = Modifier.padding(top = 10.dp, bottom = 5.dp),
                fontSize = 11.sp,
                color = MaterialTheme.colors.onSurface
            )
            Row(
                Modifier
                    .background(MaterialTheme.colors.surface, shape = RoundedCornerShape(5.dp))
                    .height(40.dp)
                    .padding(horizontal = 15.dp, vertical = 5.dp)
            ) {
                DropdownMenu(
                    selectedIndex = matching,
                    onSelectedChange = { matching = it },
                    list = matchingRules,
                )
            }

            Spacer(modifier = Modifier.padding(10.dp))
            // 选择代理方式
            Text(
                stringResource(Res.string.proxyType),
                modifier = Modifier.padding(top = 10.dp, bottom = 5.dp),
                fontSize = 11.sp,
                color = MaterialTheme.colors.onSurface
            )
            Row(
                Modifier
                    .background(MaterialTheme.colors.surface, shape = RoundedCornerShape(5.dp))
                    .height(40.dp)
                    .padding(horizontal = 15.dp, vertical = 5.dp)
            ) {
                DropdownMenu(
                    selectedIndex = proxyType,
                    onSelectedChange = { proxyType = it },
                    list = proxyRules
                )
            }


            Spacer(modifier = Modifier.padding(10.dp))
            // 启用开关
            Text(
                stringResource(Res.string.enableOrNot),
                modifier = Modifier.padding(top = 10.dp, bottom = 5.dp),
                fontSize = 11.sp,
                color = MaterialTheme.colors.onSurface
            )
            Row(
                modifier = Modifier
                    .background(MaterialTheme.colors.surface, shape = RoundedCornerShape(5.dp))
                    .height(40.dp)
                    .padding(horizontal = 15.dp, vertical = 5.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    stringResource(Res.string.enableOrNot),
                    Modifier.weight(1f).padding(0.dp),
                    fontSize = 13.sp,
                    lineHeight = 13.sp,
                    color = MaterialTheme.colors.onSurface
                )
                Switch(
                    checked = enable == 1,
                    onCheckedChange = { enable = if (it) 1 else 0 },
                    colors = appSwitchColors()
                )
            }

            Spacer(modifier = Modifier.padding(10.dp))
            // 直连优先开关
            Text(
                stringResource(Res.string.directConnPriority),
                modifier = Modifier.padding(top = 10.dp, bottom = 5.dp),
                fontSize = 11.sp,
                color = MaterialTheme.colors.onSurface
            )
            Row(
                modifier = Modifier
                    .background(MaterialTheme.colors.surface, shape = RoundedCornerShape(5.dp))
                    .height(40.dp)
                    .padding(horizontal = 15.dp, vertical = 5.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    stringResource(Res.string.directConnPriority),
                    Modifier.weight(1f).padding(0.dp),
                    fontSize = 13.sp,
                    lineHeight = 13.sp,
                    color = MaterialTheme.colors.onSurface
                )
                Switch(
                    checked = directConnPriority == 1,
                    onCheckedChange = { directConnPriority = if (it) 1 else 0 },
                    colors = appSwitchColors()
                )
            }
        }
    }
}

