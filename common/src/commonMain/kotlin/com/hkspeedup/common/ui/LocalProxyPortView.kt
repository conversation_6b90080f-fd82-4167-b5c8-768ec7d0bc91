package com.hkspeedup.common.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.hutool.core.util.StrUtil
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.lib.FlyShadowCore
import com.hkspeedup.common.service.ConfigService
import com.hkspeedup.common.service.config
import com.hkspeedup.common.ui.component.BackNavBar
import com.hkspeedup.common.ui.component.CustomTextField
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.util.SystemProxyUtil
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.viewmodel.ViewModel
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.errorPort
import flyshadow.common.generated.resources.localProxyPort
import flyshadow.common.generated.resources.save
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource

@Composable
fun LocalProxyPortView(viewModel: ViewModel) {
    Box(
        Modifier.padding(10.dp).background(MaterialTheme.colors.surface, shape = RoundedCornerShape(10.dp))
    ) {
        var proxyPort by remember { mutableStateOf(config.proxyPort.toString()) }

        // 自定义规则列表标题
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            BackNavBar(stringResource(Res.string.localProxyPort)) {
                TextButton(onClick = {
                    ViewFunction.setWindowLoading(true)
                    GlobalScope.launch(Dispatchers.IO) {
                        kotlin.runCatching {
                            if (proxyPort.isNotEmpty() && (proxyPort.all { char -> char.isDigit() } && proxyPort.toLong() >= 0)) {
                                config.proxyPort = proxyPort.toInt()
                                val errorMessage = FlyShadowCore.startProxy(config.proxyPort)
                                if (StrUtil.isNotBlank(errorMessage)) {
                                    ViewFunction.showAlertDialog(
                                        AlertDialogMessage(
                                            StringContentVo(
                                                null,
                                                errorMessage
                                            )
                                        )
                                    )
                                } else {
                                    if (viewModel.proxyStatus == ProxyStatus.START) {
                                        if (config.autoSetSysProxyOnStartUp) {
                                            SystemProxyUtil.enableProxy("127.0.0.1", config.proxyPort)
                                        }
                                    }
                                    ConfigService.save()
                                    ViewFunction.backNavigationStackPage()
                                }

                            } else {
                                ViewFunction.showAlertDialog(
                                    AlertDialogMessage(
                                        StringContentVo(
                                            Res.string.errorPort,
                                            null
                                        )
                                    )
                                )
                            }
                        }.onFailure {
                            GlobalLog.error(it, "设置代理端口失败")
                            ViewFunction.showAlertDialog(
                                AlertDialogMessage(
                                    StringContentVo(
                                        Res.string.errorPort,
                                        it.message
                                    )
                                )
                            )
                        }
                        ViewFunction.setWindowLoading(false)
                    }
                }, modifier = Modifier.enableFocusable()) {
                    Text(stringResource(Res.string.save))
                }
            }
            Divider(Modifier.padding(start = 10.dp))


            CustomTextField(
                keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Number),
                value = proxyPort,
                onValueChange = {
                    proxyPort = it
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .enableFocusable()
                    .padding(horizontal = 15.dp, vertical = 5.dp)
                    .background(MaterialTheme.colors.surface)
                    .height(30.dp),
                singleLine = true,
                textStyle = TextStyle(fontSize = 12.sp),
                contentPadding = PaddingValues(5.dp)
            )
        }

    }

}