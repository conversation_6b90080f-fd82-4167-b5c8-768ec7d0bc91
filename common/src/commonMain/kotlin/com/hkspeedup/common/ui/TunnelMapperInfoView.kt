package com.hkspeedup.common.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.ContentAlpha
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.hutool.core.date.BetweenFormatter
import cn.hutool.core.util.StrUtil
import com.google.gson.reflect.TypeToken
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.lib.FlyShadowCore
import com.hkspeedup.common.ui.component.BackNavBar
import com.hkspeedup.common.ui.component.CustomOutlinedTextField
import com.hkspeedup.common.ui.component.DropdownMenu
import com.hkspeedup.common.ui.component.MyVerticalScrollbar
import com.hkspeedup.common.util.DateUtil
import com.hkspeedup.common.util.GsonUtil
import com.hkspeedup.common.util.TrafficUtil
import com.hkspeedup.common.vo.TunnelMapperInfoVo
import flyshadow.common.generated.resources.*
import kotlinx.coroutines.*
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import java.util.*


fun sort(tunnelMapperInfoVos: SnapshotStateList<TunnelMapperInfoVo>, selectedIndex: Int) {
    // 下标对应的字段
    val sortingFunction: (TunnelMapperInfoVo) -> Comparable<*>? = when (selectedIndex) {
        0, 1 -> { it -> it.active_time }
        2, 3 -> { it -> it.source_addr }
        4, 5 -> { it -> it.target_addr }
        6, 7 -> { it -> it.protocol }
        8, 9 -> { it -> it.proxy_type }
        10, 11 -> { it -> it.matcher_name }
        12, 13 -> { it -> it.matcher_rule }
        14, 15 -> { it -> it.upload_traffic }
        16, 17 -> { it -> it.download_traffic }
        18, 19 -> { it -> it.upload_traffic_total }
        20, 21 -> { it -> it.download_traffic_total }
        else -> { _ -> null }
    }

    sortingFunction.let {
        if (selectedIndex % 2 == 0) {
            // 偶数：正序
            tunnelMapperInfoVos.sortWith(compareBy(sortingFunction))
        } else {
            // 奇数：倒序
            tunnelMapperInfoVos.sortWith(compareByDescending(sortingFunction))
        }
    }
}

@OptIn(DelicateCoroutinesApi::class, ExperimentalLayoutApi::class)
@Composable
fun TunnelMapperInfoView() {
    val tunnelMapperInfoVos = remember { mutableStateListOf<TunnelMapperInfoVo>() }
    val nowTime = remember { mutableStateOf(Date()) }
    var searchText by remember { mutableStateOf(TextFieldValue()) }
    var selectedIndex by remember { mutableStateOf(0) }

    DisposableEffect(Unit) {
        val job = GlobalScope.launch(Dispatchers.IO) {
            while (true) {
                nowTime.value = Date()
                val tunnelMapperInfoVo: List<TunnelMapperInfoVo> = GsonUtil.createGson().fromJson(
                    FlyShadowCore.getTunnelMapperInfoJson(),
                    object : TypeToken<List<TunnelMapperInfoVo>>() {}.type
                )

                val keyList: MutableSet<String> = HashSet(tunnelMapperInfoVo.map {
                    String.format(
                        "%s:%s===%s",
                        it.source_addr,
                        it.source_port,
                        it.target_addr,
                        it.target_port
                    )
                }).toMutableSet()

                withContext(Dispatchers.Main) {
                    tunnelMapperInfoVos.removeIf {
                        !keyList.contains(
                            String.format(
                                "%s:%s===%s",
                                it.source_addr,
                                it.source_port,
                                it.target_addr,
                                it.target_port
                            )
                        )
                    }
                }

                val addList = arrayListOf<TunnelMapperInfoVo>();
                tunnelMapperInfoVo.forEach {
                    val key = String.format(
                        "%s:%s===%s",
                        it.source_addr,
                        it.source_port,
                        it.target_addr,
                        it.target_port
                    );
                    for (mapperInfoVo in tunnelMapperInfoVos) {
                        if (String.format(
                                "%s:%s===%s",
                                mapperInfoVo.source_addr,
                                mapperInfoVo.source_port,
                                mapperInfoVo.target_addr,
                                mapperInfoVo.target_port
                            ) == key
                        ) {
                            mapperInfoVo.matcher_name = it.matcher_name
                            mapperInfoVo.matcher_rule = it.matcher_rule
                            mapperInfoVo.process_name = it.process_name
                            mapperInfoVo.proxy_type = it.proxy_type
                            mapperInfoVo.active_time = it.active_time
                            mapperInfoVo.upload_traffic = it.upload_traffic
                            mapperInfoVo.download_traffic = it.download_traffic
                            mapperInfoVo.upload_traffic_total = it.upload_traffic_total
                            mapperInfoVo.download_traffic_total = it.download_traffic_total
                            return@forEach
                        }
                    }
                    addList.add(it)
                }

                withContext(Dispatchers.Main) {
                    tunnelMapperInfoVos.addAll(addList)

                    sort(tunnelMapperInfoVos, selectedIndex)
                }

                delay(1000)
            }
        }
        // 使用 DisposableEffect 来清理资源或取消工作
        onDispose {
            job.cancel() // 在界面消失时取消协程
        }
    }

    Box(
        Modifier.padding(10.dp)
            .background(MaterialTheme.colors.surface, shape = RoundedCornerShape(10.dp))
    ) {
        val state = rememberLazyListState()

        // 连接列表标题
        Column {
            BackNavBar(stringResource(Res.string.connectList)) {
                val proxyRules = listOf(
                    "↑ ${stringResource(Res.string.createTime)}",
                    "↓ ${stringResource(Res.string.createTime)}",
                    "↑ ${stringResource(Res.string.sourceAddress)}",
                    "↓ ${stringResource(Res.string.sourceAddress)}",
                    "↑ ${stringResource(Res.string.targetAddress)}",
                    "↓ ${stringResource(Res.string.targetAddress)}",
                    "↑ ${stringResource(Res.string.protocol)}",
                    "↓ ${stringResource(Res.string.protocol)}",
                    "↑ ${stringResource(Res.string.proxyType)}",
                    "↓ ${stringResource(Res.string.proxyType)}",
                    "↑ ${stringResource(Res.string.matchName)}",
                    "↓ ${stringResource(Res.string.matchName)}",
                    "↑ ${stringResource(Res.string.matchRule)}",
                    "↓ ${stringResource(Res.string.matchRule)}",
                    "↑ ${stringResource(Res.string.uploadTraffic)}",
                    "↓ ${stringResource(Res.string.uploadTraffic)}",
                    "↑ ${stringResource(Res.string.downloadTraffic)}",
                    "↓ ${stringResource(Res.string.downloadTraffic)}",
                    "↑ ${stringResource(Res.string.uploadTrafficTotal)}",
                    "↓ ${stringResource(Res.string.uploadTrafficTotal)}",
                    "↑ ${stringResource(Res.string.downloadTrafficTotal)}",
                    "↓ ${stringResource(Res.string.downloadTrafficTotal)}",
                )
                Row(Modifier.width(200.dp).padding(end = 5.dp)) {
                    DropdownMenu(
                        modifier = Modifier.enableFocusable(),
                        selectedIndex = selectedIndex,
                        onSelectedChange = {
                            selectedIndex = it
                            sort(tunnelMapperInfoVos, selectedIndex)
                        },
                        list = proxyRules
                    )
                }
            }

            Divider(Modifier.padding(start = 10.dp))

            CustomOutlinedTextField(
                value = searchText,
                onValueChange = { searchText = it },
                label = { Text(stringResource(Res.string.searchContent), modifier = Modifier.padding(0.dp)) },
                placeholder = { Text(stringResource(Res.string.searchContent), modifier = Modifier.padding(0.dp)) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(10.dp)
                    .enableFocusable()
                    .height(50.dp),
                singleLine = true, // 确保输入框为单行
                contentPadding = PaddingValues(5.dp)
            )

            Divider(Modifier.padding(start = 10.dp))

            if (tunnelMapperInfoVos.isEmpty()) {
                return
            }

            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 连接列表
                LazyColumn(state = state, modifier = Modifier.padding(start = 5.dp, end = 5.dp)) {
                    val connectList =
                        ArrayList(tunnelMapperInfoVos).filter {
                            val textSplit = searchText.text.lowercase().split(" ")
                            var result = true
                            for (s in textSplit) {
                                result = result && it.protocol.lowercase().contains(s) ||
                                        it.source_addr.lowercase().contains(s) ||
                                        it.source_port.toString().contains(s) ||
                                        it.target_addr.lowercase().contains(s) ||
                                        it.target_port.toString().contains(s) ||
                                        it.matcher_name.lowercase().contains(s) ||
                                        it.matcher_rule.lowercase().contains(s) ||
                                        it.proxy_type.lowercase().contains(s) ||
                                        it.process_name.lowercase().contains(s)
                            }
                            result
                        }

                    items(connectList.size) { index ->
                        SelectionContainer {
                            val tunnelMapperInfoVo = connectList[index]

                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Column(Modifier.weight(1f)) {

                                    FlowRow {
                                        Text(
                                            String.format(
                                                "%s:%s -> %s:%s",
                                                tunnelMapperInfoVo.source_addr,
                                                tunnelMapperInfoVo.source_port,
                                                tunnelMapperInfoVo.target_addr,
                                                tunnelMapperInfoVo.target_port,
                                            ),
                                            color = MaterialTheme.colors.onSurface,
                                            fontSize = 13.sp,
                                            lineHeight = 13.sp
                                        )
                                    }

                                    FlowRow(Modifier.fillMaxWidth()) {
                                        if (StrUtil.isNotBlank(tunnelMapperInfoVo.protocol)) {
                                            Text(
                                                tunnelMapperInfoVo.protocol,
                                                fontSize = 13.sp,
                                                lineHeight = 1.sp,
                                                color = Color.White,
                                                modifier = Modifier.padding(3.dp)
                                                    .background(
                                                        Color(color = 0xFFe16531),
                                                        shape = RoundedCornerShape(3.dp)
                                                    )
                                                    .padding(3.dp)
                                            )
                                        }
                                        if (StrUtil.isNotBlank(tunnelMapperInfoVo.proxy_type)) {
                                            Text(
                                                tunnelMapperInfoVo.proxy_type,
                                                fontSize = 13.sp,
                                                lineHeight = 1.sp,
                                                color = Color.White,
                                                modifier = Modifier.padding(3.dp)
                                                    .background(
                                                        Color(color = 0xCFe16531),
                                                        shape = RoundedCornerShape(3.dp)
                                                    )
                                                    .padding(3.dp)
                                            )
                                        }
                                        if (StrUtil.isNotBlank(tunnelMapperInfoVo.matcher_name)) {
                                            var text = tunnelMapperInfoVo.matcher_name
                                            if (StrUtil.isNotBlank(tunnelMapperInfoVo.matcher_rule)) {
                                                text += "(${tunnelMapperInfoVo.matcher_rule})"
                                            }
                                            Text(
                                                text,
                                                fontSize = 13.sp,
                                                lineHeight = 1.sp,
                                                color = Color.White,
                                                modifier = Modifier.padding(3.dp)
                                                    .background(
                                                        Color(color = 0x8Be16531),
                                                        shape = RoundedCornerShape(3.dp)
                                                    )
                                                    .padding(3.dp)
                                            )
                                        }
                                        if (StrUtil.isNotBlank(tunnelMapperInfoVo.process_name)) {
                                            Text(
                                                tunnelMapperInfoVo.process_name,
                                                fontSize = 13.sp,
                                                lineHeight = 1.sp,
                                                color = Color.White,
                                                modifier = Modifier.padding(3.dp)
                                                    .background(
                                                        Color(color = 0x65AB5BFF),
                                                        shape = RoundedCornerShape(3.dp)
                                                    )
                                                    .padding(3.dp)
                                            )
                                        }

                                        Text(
                                            DateUtil.formatBetween(
                                                nowTime.value,
                                                Date(tunnelMapperInfoVo.active_time),
                                                BetweenFormatter.Level.SECOND
                                            ),
                                            fontSize = 13.sp,
                                            lineHeight = 1.sp,
                                            modifier = Modifier.padding(3.dp)
                                                .background(Color(color = 0x991196db), shape = RoundedCornerShape(3.dp))
                                                .padding(3.dp)
                                        )
                                        Text(
                                            DateUtil.formatDateTime(
                                                Date(tunnelMapperInfoVo.active_time),
                                                "yyyy-MM-dd HH:mm:ss"
                                            ),
                                            fontSize = 13.sp,
                                            lineHeight = 1.sp,
                                            modifier = Modifier.padding(3.dp)
                                                .background(Color(color = 0x651196db), shape = RoundedCornerShape(3.dp))
                                                .padding(3.dp)
                                        )
                                        Text(
                                            "↑ ${
                                                TrafficUtil.getTrafficString(
                                                    tunnelMapperInfoVo.upload_traffic,
                                                    null
                                                )
                                            }/S ↓ ${
                                                TrafficUtil.getTrafficString(
                                                    tunnelMapperInfoVo.download_traffic,
                                                    null
                                                )
                                            }/S",
                                            fontSize = 13.sp,
                                            lineHeight = 1.sp,
                                            color = Color.White,
                                            modifier = Modifier.padding(3.dp)
                                                .background(Color(color = 0x6511c6db), shape = RoundedCornerShape(3.dp))
                                                .padding(3.dp)
                                        )
                                        Text(
                                            "↑ ${
                                                TrafficUtil.getTrafficString(
                                                    tunnelMapperInfoVo.upload_traffic_total,
                                                    null
                                                )
                                            }/S ↓${
                                                TrafficUtil.getTrafficString(
                                                    tunnelMapperInfoVo.download_traffic_total,
                                                    null
                                                )
                                            }/S Total",
                                            fontSize = 13.sp,
                                            lineHeight = 1.sp,
                                            color = Color.White,
                                            modifier = Modifier.padding(3.dp)
                                                .background(Color(color = 0x6511a6db), shape = RoundedCornerShape(3.dp))
                                                .padding(3.dp)
                                        )
                                    }

                                    Divider()
                                }

                                Image(
                                    painter = painterResource(Res.drawable.x),
                                    contentDescription = "",
                                    modifier = Modifier.size(25.dp)
                                        .enableFocusable()
                                        .clickable {
                                            FlyShadowCore.closeConnect("${tunnelMapperInfoVo.source_addr}:${tunnelMapperInfoVo.source_port}===${tunnelMapperInfoVo.protocol}")
                                        }
                                )
                            }
                        }

                    }
                }
                MyVerticalScrollbar(
                    modifier = Modifier.align(Alignment.CenterEnd),
                    state = state
                )
            }
        }

    }

}