package com.hkspeedup.common.ui.component

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.hkspeedup.common.viewmodel.ViewFunction

@Composable
fun InitLoading() {
    var showWindowLoading by remember { mutableStateOf(false) }

    ViewFunction.setWindowLoading = { showWindowLoading = it }

    if (showWindowLoading) {
        Box(
            Modifier.background(color = MaterialTheme.colors.primaryVariant.copy(alpha = 0.5f)).fillMaxSize()
                .clickable(enabled = false, onClick = {}),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    }
}