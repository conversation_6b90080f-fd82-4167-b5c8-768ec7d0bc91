package com.hkspeedup.common.ui

import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.lib.FlyShadowCore
import com.hkspeedup.common.service.ConfigService
import com.hkspeedup.common.service.ProxyService
import com.hkspeedup.common.service.SubscribeService
import com.hkspeedup.common.service.config
import com.hkspeedup.common.ui.component.MyVerticalScrollbar
import com.hkspeedup.common.ui.theme.appSwitchColors
import com.hkspeedup.common.util.DateUtil
import com.hkspeedup.common.util.TrafficUtil
import com.hkspeedup.common.viewmodel.*
import com.hkspeedup.common.vo.NodeInfo
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import java.util.*


@Composable
fun NodesPage(viewModel: ViewModel) {
    Column(Modifier.fillMaxHeight().padding(bottom = 70.dp)) {
        val nodeList = remember { config.subscribeInfo?.node.orEmpty().toMutableStateList() }
        var selectNodeName by remember { mutableStateOf(config.selectNodeName) }
        var delaySortType by remember { mutableStateOf(config.delaySortType) }
        val starNodeNameList = remember { config.starNodeNameList.toMutableStateList() }
        val starNodeCnNameList = remember { config.starNodeCnNameList.toMutableStateList() }

        // 设置菜单
        Column(
            Modifier.fillMaxWidth().padding(20.dp).background(MaterialTheme.colors.surface, shape = RoundedCornerShape(10.dp))
        ) {
            proxySwitchView(selectNodeName, viewModel)
            Divider(Modifier.padding(start = 40.dp))
            subscribeSettingView(viewModel, nodeList)
            Divider(Modifier.padding(start = 40.dp))
            proxyRuleView(viewModel)
            Divider(Modifier.padding(start = 40.dp))
            nodeDelayTestView(nodeList)
        }

        // 节点列表
        Box(
            Modifier.padding(start = 20.dp, bottom = 20.dp, end = 20.dp)
                .background(MaterialTheme.colors.surface, shape = RoundedCornerShape(10.dp))
        ) {
            val state = rememberLazyListState()
            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 节点行
                Column {
                    Row(
                        modifier = Modifier.fillMaxWidth().height(35.dp).padding(start = 20.dp, end = 8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            painter = painterResource(Res.drawable.list),
                            contentDescription = "", modifier = Modifier.size(19.dp).offset(x = (-10).dp)
                        )
                        Column(Modifier.weight(1f)) {
                            Text(
                                stringResource(Res.string.serverList),
                                fontSize = 13.sp,
                                lineHeight = 1.sp
                            )

                            // 用户信息
                            config.subscribeInfo?.user?.let {
                                val redColor =
                                    ((it.trafficDownload ?: 0) + (it.trafficUpload ?: 0)) > (it.trafficSize ?: 0)
                                            || Date().time > ((it.expireTime?.time ?: 0) - 1000 * 60 * 60 * 24 * 2)
                                Text(
                                    "${stringResource(Res.string.expire)}: ${
                                        DateUtil.formatDateTime(
                                            it.expireTime,
                                            "yyyy-MM-dd HH:mm:ss"
                                        )
                                    } ${stringResource(Res.string.traffic)}: ${
                                        TrafficUtil.getTrafficString(
                                            (it.trafficDownload ?: 0) + (it.trafficUpload ?: 0),
                                            null
                                        )
                                    } / ${
                                        TrafficUtil.getTrafficString(
                                            it.trafficSize ?: 0,
                                            null
                                        )
                                    }",
                                    fontSize = 8.sp,
                                    lineHeight = 1.sp,
                                    color = if (redColor) MaterialTheme.colors.error else MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                                )
                            }
                        }
                        when (delaySortType) {
                            0 -> Image(
                                painter = painterResource(Res.drawable.sort_list), contentDescription = "",
                                modifier = Modifier.size(15.dp).enableFocusable()
                                    .clickable {
                                        delaySortType = 1
                                        config.delaySortType = delaySortType
                                        ConfigService.save()
                                    })

                            1 -> Image(
                                painter = painterResource(Res.drawable.sort_up), contentDescription = "",
                                modifier = Modifier.size(15.dp).enableFocusable()
                                    .clickable {
                                        delaySortType = 2
                                        config.delaySortType = delaySortType
                                        ConfigService.save()
                                    })

                            2 -> Image(
                                painter = painterResource(Res.drawable.sort_down), contentDescription = "",
                                modifier = Modifier.size(15.dp).enableFocusable()
                                    .clickable {
                                        delaySortType = 0
                                        config.delaySortType = delaySortType
                                        ConfigService.save()
                                    })
                        }
                    }
                    Divider(Modifier.padding(start = 40.dp))
                    // 节点列表
                    var preSelectedIndex by remember { mutableStateOf(0) }
                    var selectedIndex by remember { mutableStateOf(0) }
                    LazyColumn(state = state) {
                        val list = synchronized(nodeList) {
                            // 排序
                            nodeList.sortedWith(
                                // 收藏的排最前
                                compareByDescending<NodeInfo> {
                                    (if (viewModel.language == "ZH") starNodeCnNameList else starNodeNameList).contains(
                                        if (viewModel.language == "ZH") {
                                            it.cnName
                                        } else {
                                            it.name
                                        }
                                    )
                                }
                                    // 再根据策略排序
                                    .let { it ->
                                        when (delaySortType) {
                                            0 -> it.thenByDescending {
                                                if (viewModel.language == "ZH") it.cnName else it.name
                                            }

                                            1 -> it.thenBy { (it.delay ?: -1) < 0 }.thenBy { it.delay }
                                            2 -> it.thenBy { (it.delay ?: -1) < 0 }.thenByDescending { it.delay }
                                            else -> it.thenByDescending {
                                                if (viewModel.language == "ZH") it.cnName else it.name
                                            }
                                        }
                                    }
                            )
                        }
                        //  节点 Items
                        items(list.size) { index ->
                            val nodeInfo = list[index]
                            Column(Modifier.fillMaxWidth().enableFocusable().clickable {
                                preSelectedIndex = selectedIndex
                                val selectName = if (viewModel.language == "ZH") nodeInfo.cnName else nodeInfo.name

                                config.selectNodeName = selectName
                                selectNodeName = selectName
                                ConfigService.save()
                                ProxyService.reSelectTunnel()
                            }) {
                                Row(
                                    Modifier.padding(start = 10.dp, end = 5.dp).height(35.dp).fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically,
                                ) {

                                    // 选中框动画效果
                                    val primaryColor = MaterialTheme.colors.primary
                                    val isSelected =
                                        selectNodeName == if (viewModel.language == "ZH") nodeInfo.cnName else nodeInfo.name

                                    val height by animateIntAsState(
                                        targetValue = if (isSelected) 35 else 0,
                                        animationSpec = tween(durationMillis = 300)
                                    )
                                    val contentAlignment = if (isSelected) {
                                        selectedIndex = index
                                        if (preSelectedIndex >= index) Alignment.BottomEnd else Alignment.TopStart
                                    } else {
                                        if (selectedIndex >= index) Alignment.BottomEnd else Alignment.TopStart
                                    }
                                    Box(Modifier.height(35.dp).width(7.dp), contentAlignment = contentAlignment) {
                                        Box(
                                            Modifier.height(height.dp).width(7.dp)
                                                .clip(RoundedCornerShape(7.dp))
                                                .background(color = MaterialTheme.colors.onSurface.copy(alpha = 0.2f))
                                        ) {
                                            Box(
                                                Modifier.height(height.dp).width(4.dp)
                                                    .clip(RoundedCornerShape(4.dp))
                                                    .background(color = primaryColor)
                                            )
                                        }
                                    }


                                    Spacer(Modifier.width(5.dp))

                                    // 节点信息
                                    Column(modifier = Modifier.weight(1f)) {
                                        // 节点名字
                                        (if (viewModel.language == "ZH") nodeInfo.cnName else nodeInfo.name)
                                            ?.let { it1 ->
                                                Text(
                                                    it1,
                                                    fontSize = 13.sp,
                                                    maxLines = 1,
                                                    lineHeight = 13.sp,
                                                )
                                            }
                                        // 描述
                                        Row {
                                            Column(
                                                Modifier.padding(horizontal = 3.dp).border(
                                                    width = 1.dp,
                                                    shape = RoundedCornerShape(3.dp),
                                                    color = MaterialTheme.colors.onSurface.copy(0.3f)
                                                )
                                            ) {
                                                Text(
                                                    "Udp over Tcp",
                                                    fontSize = 9.sp,
                                                    lineHeight = 9.sp,
                                                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                                                    modifier = Modifier.padding(1.dp)
                                                )
                                            }
                                            nodeInfo.nativeUdp?.let { it1 ->
                                                if (it1.toInt() == 1) {
                                                    Column(
                                                        Modifier.padding(horizontal = 3.dp).border(
                                                            width = 1.dp,
                                                            shape = RoundedCornerShape(3.dp),
                                                            color = MaterialTheme.colors.error.copy(0.3f)
                                                        )
                                                    ) {
                                                        Text(
                                                            stringResource(Res.string.nativeUdp),
                                                            fontSize = 9.sp,
                                                            maxLines = 1,
                                                            lineHeight = 9.sp,
                                                            color = MaterialTheme.colors.error.copy(0.5f),
                                                            modifier = Modifier.padding(1.dp),
                                                        )
                                                    }
                                                }
                                            }
                                            Column(
                                                Modifier.padding(horizontal = 3.dp).border(
                                                    width = 1.dp,
                                                    shape = RoundedCornerShape(3.dp),
                                                    color = MaterialTheme.colors.onSurface.copy(0.3f)
                                                )
                                            ) {
                                                Text(
                                                    "${stringResource(Res.string.trafficMultiplier)} ${nodeInfo.trafficRate ?: 1}x",
                                                    fontSize = 9.sp,
                                                    lineHeight = 9.sp,
                                                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                                                    modifier = Modifier.padding(1.dp)
                                                )
                                            }
                                        }
                                    }

                                    // 延迟
                                    nodeInfo.delay?.let { it1 ->
                                        if (it1 == -2L) {
                                            CircularProgressIndicator(
                                                modifier = Modifier.size(15.dp),
                                                strokeWidth = 2.dp
                                            )
                                        } else {
                                            val color = when (it1) {
                                                -1L -> MaterialTheme.colors.error
                                                in 0L..200L -> Color(0xFF4CAF50) // 绿色
                                                in 200L..500L -> MaterialTheme.colors.primary
                                                else -> MaterialTheme.colors.error
                                            }
                                            Text(
                                                text = if (it1 == -1L) stringResource(Res.string.timeout) else "${it1}ms",
                                                color = color.copy(alpha = 0.8f),
                                                lineHeight = 1.sp,
                                                fontSize = 12.sp
                                            )
                                        }
                                    }

                                    // 收藏
                                    if ((if (viewModel.language == "ZH") starNodeCnNameList else starNodeNameList)
                                            .contains(if (viewModel.language == "ZH") nodeInfo.cnName else nodeInfo.name)
                                    ) {
                                        Image(
                                            painter = painterResource(Res.drawable.star_fill),
                                            contentDescription = "",
                                            modifier = Modifier.size(19.dp).enableFocusable().alpha(0.7f)
                                                .clickable {
                                                    config.starNodeCnNameList.remove(nodeInfo.cnName)
                                                    starNodeCnNameList.remove(nodeInfo.cnName)
                                                    config.starNodeNameList.remove(nodeInfo.name)
                                                    starNodeNameList.remove(nodeInfo.name)
                                                    ConfigService.save()
                                                }
                                        )
                                    } else {
                                        Image(
                                            painter = painterResource(Res.drawable.star),
                                            contentDescription = "",
                                            modifier = Modifier.size(19.dp).enableFocusable().alpha(0.7f)
                                                .clickable {
                                                    nodeInfo.name?.let {
                                                        config.starNodeNameList.add(it)
                                                        starNodeNameList.add(it)
                                                    }
                                                    nodeInfo.cnName?.let {
                                                        config.starNodeCnNameList.add(it)
                                                        starNodeCnNameList.add(it)
                                                    }
                                                    ConfigService.save()
                                                }
                                        )
                                    }
                                }
                                Divider(Modifier.padding(start = 20.dp))
                            }
                        }
                    }
                }
            }
            MyVerticalScrollbar(
                modifier = Modifier.align(Alignment.CenterEnd),
                state = state
            )
        }
    }
}

@Composable
fun proxySwitchView(selectNodeName: String?, viewModel: ViewModel) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.node_switch),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Column {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        selectNodeName ?: stringResource(Res.string.pleaseSelectServer),
                        lineHeight = 1.sp,
                        fontSize = 13.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.padding(start = 5.dp).widthIn(max = 200.dp)
                    )
                    if (config.nativeUdp && config.getSelectNodeInfo()?.nativeUdp?.toInt() == 1) {
                        Column(
                            Modifier.padding(horizontal = 3.dp).border(
                                width = 1.dp,
                                shape = RoundedCornerShape(3.dp),
                                color = MaterialTheme.colors.error.copy(0.3f)
                            )
                        ) {
                            Text(
                                "NativeUDP",
                                fontSize = 9.sp,
                                maxLines = 1,
                                lineHeight = 9.sp,
                                color = MaterialTheme.colors.error.copy(0.5f),
                                modifier = Modifier.padding(1.dp),
                            )
                        }
                    }
                }
                Row(Modifier.padding(start = 5.dp)) {
                    var uploadTraffic by remember { mutableStateOf(0) }
                    ViewFunction.setUploadTraffic = { uploadTraffic = it }
                    Text(
                        "↑ ${TrafficUtil.getTrafficString(uploadTraffic.toLong(), null)}/S",
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                        lineHeight = 1.sp,
                        fontSize = 8.sp
                    )
                    Spacer(Modifier.width(5.dp))
                    var downloadTraffic by remember { mutableStateOf(0) }
                    ViewFunction.setDownloadTraffic = { downloadTraffic = it }
                    Text(
                        "↓ ${TrafficUtil.getTrafficString(downloadTraffic.toLong(), null)}/S",
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                        lineHeight = 1.sp,
                        fontSize = 8.sp
                    )
                }
            }
            Spacer(Modifier.weight(weight = 1f))
            if (viewModel.proxyStatus == ProxyStatus.CONNECTING) {
                CircularProgressIndicator(modifier = Modifier.size(15.dp), strokeWidth = 2.dp)
            }

            Switch(
                modifier = Modifier.enableFocusable().scale(1.2f),
                checked = viewModel.proxyStatus != ProxyStatus.STOP, onCheckedChange = {
                    if (it) {
                        viewModel.proxyStatus = ProxyStatus.CONNECTING
                        ProxyService.startTunnel()
                    } else {
                        ProxyService.stopTunnel {}
                    }
                },
                colors = appSwitchColors()
            )
        }
    }
}


@Composable
private fun proxyRuleView(viewModel: ViewModel) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 10.dp).enableFocusable(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.proxy),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            if (viewModel.tunEnable) {
                Text(
                    stringResource(Res.string.proxyMode),
                    fontSize = 13.sp,
                    modifier = Modifier.padding(start = 5.dp),
                    lineHeight = 1.sp
                )
                Text(
                    "(TUN)",
                    fontSize = 13.sp,
                    color = MaterialTheme.colors.error,
                    modifier = Modifier.weight(weight = 1f),
                    lineHeight = 1.sp
                )
            } else {
                Text(
                    stringResource(Res.string.proxyMode),
                    fontSize = 13.sp,
                    modifier = Modifier.padding(start = 5.dp).weight(weight = 1f),
                    lineHeight = 1.sp
                )
            }

            val getSelectTabIndex = {
                when (config.proxyType) {
                    0 -> 0
                    2 -> 1
                    1 -> 2
                    else -> 1
                }
            }

            var selectedTabIndex by remember {
                mutableStateOf(
                    getSelectTabIndex()
                )
            }

            DisposableEffect(Unit) {
                val job = GlobalScope.launch(Dispatchers.IO) {
                    while (true) {
                        selectedTabIndex = getSelectTabIndex()
                        delay(3000)
                    }
                }
                onDispose {
                    job.cancel() // 在界面消失时取消协程
                }
            }

            TabRow(
                backgroundColor = MaterialTheme.colors.surface,
                contentColor = MaterialTheme.colors.primary,
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.width(155.dp).clip(RoundedCornerShape(5.dp))
            ) {
                Tab(
                    modifier = Modifier.width(70.dp).height(25.dp).enableFocusable(),
                    selected = selectedTabIndex == 0,
                    onClick = {
                        FlyShadowCore.setProxyType(0)
                        config.proxyType = 0
                        selectedTabIndex = 0
                        ConfigService.save()
                    }
                ) {
                    Text(
                        stringResource(Res.string.direct), fontSize = 13.sp, lineHeight = 1.sp,
                        modifier = Modifier.height(20.dp)
                    )
                }
                Tab(
                    modifier = Modifier.width(45.dp).height(25.dp).enableFocusable(),
                    selected = selectedTabIndex == 1,
                    onClick = {
                        FlyShadowCore.setProxyType(3)
                        config.proxyType = 2
                        selectedTabIndex = 1
                        ConfigService.save()
                    }
                ) {
                    Text(
                        stringResource(Res.string.rule), fontSize = 13.sp, lineHeight = 1.sp,
                        modifier = Modifier.height(20.dp)
                    )
                }
                Tab(
                    modifier = Modifier.width(50.dp).height(25.dp).enableFocusable(),
                    selected = selectedTabIndex == 2,
                    onClick = {
                        FlyShadowCore.setProxyType(2)
                        config.proxyType = 1
                        selectedTabIndex = 2
                        ConfigService.save()
                    }
                ) {
                    Text(
                        stringResource(Res.string.global), fontSize = 13.sp, lineHeight = 1.sp,
                        modifier = Modifier.height(20.dp)
                    )
                }
            }
        }
    }
}


@Composable
private fun subscribeSettingView(
    viewModel: ViewModel,
    nodeList: SnapshotStateList<NodeInfo>,
) {
    // 订阅设置
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth()
            .height(35.dp)
            .clip(RoundedCornerShape(10.dp))
            .enableFocusable()
    ) {
        Row(
            Modifier.padding(horizontal = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                Modifier.weight(1f)
                    .enableFocusable()
                    .clickable {
                        ViewFunction.pushNavigationStackPage(SetSubscribeSettingPage())
                    }, verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(Res.drawable.subscribe),
                    contentDescription = stringResource(Res.string.setSubscribe), modifier = Modifier.size(19.dp)
                )
                Column(Modifier.padding(start = 5.dp).weight(weight = 1f)) {
                    Text(
                        stringResource(Res.string.setSubscribe),
                        lineHeight = 1.sp,
                        fontSize = 13.sp,
                    )
                    // 最后更新时间
                    config.lastUpdateTime?.let {

                        Text(
                            "${stringResource(Res.string.lastUpdate)}: ${
                                DateUtil.formatDateTime(
                                    it,
                                    "yyyy-MM-dd HH:mm:ss"
                                )
                            }",
                            lineHeight = 1.sp,
                            fontSize = 8.sp,
                            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
                Text(stringResource(Res.string.setting), color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f), fontSize = 13.sp, lineHeight = 1.sp)
                Image(
                    painter = painterResource(Res.drawable.right),
                    contentDescription = "",
                    modifier = Modifier.size(15.dp),
                    colorFilter = ColorFilter.tint(color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f))
                )
            }

            var showRefreshLoading by remember { mutableStateOf(false) }
            SubscribeService.updateSubscribeCallback = { it ->
                if (it == null) {
                    nodeList.clear()
                    config.subscribeInfo?.node?.let { it1 ->
                        nodeList.addAll(it1)
                    }

                    ViewFunction.backNavigationStackPage()
                    ViewFunction.showAlertDialog(
                        AlertDialogMessage(
                            StringContentVo(
                                Res.string.refreshSuccess,
                                null
                            )
                        )
                    )
                } else {
                    ViewFunction.showAlertDialog(AlertDialogMessage(it))
                }
                showRefreshLoading = false
                ViewFunction.setWindowLoading(false)
            }

            if (showRefreshLoading) {
                CircularProgressIndicator(modifier = Modifier.size(18.dp), strokeWidth = 2.dp)
            } else {
                Image(
                    painter = painterResource(Res.drawable.refresh),
                    contentDescription = "",
                    modifier = Modifier.size(20.dp)
                        .enableFocusable()
                        .clickable {
                            showRefreshLoading = true
                            SubscribeService.setSubscribeUrl(config.subscribePassword, viewModel)
                        },
                )
            }
        }
    }
}


@Composable
private fun nodeDelayTestView(nodeList: SnapshotStateList<NodeInfo>) {
    // 延迟测试
    Column(
        Modifier.fillMaxWidth()
            .height(35.dp)
            .clip(RoundedCornerShape(10.dp))
            .enableFocusable()
            .clickable(enabled = !nodeList.any { it.delay == -2L }) {
                nodeList.forEachIndexed { index, _ -> nodeList[index] = nodeList[index].copy(delay = -2) }
                ProxyService.testTunnelDelay { delay, name ->
                    val index = nodeList.indexOfFirst { it.name == name }
                    nodeList[index] = nodeList[index].copy(delay = delay)
                }
            }) {
        Box {
            Row(
                Modifier.padding(horizontal = 10.dp).fillMaxSize(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Image(
                    painter = painterResource(Res.drawable.speed_test),
                    contentDescription = stringResource(Res.string.delayTest),
                    modifier = Modifier.size(19.dp)
                )
                Text(
                    stringResource(Res.string.delayTest),
                    fontSize = 13.sp,
                    lineHeight = 1.sp,
                    modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
                )
                if (nodeList.any { it.delay == -2L }) {
                    CircularProgressIndicator(modifier = Modifier.size(15.dp), strokeWidth = 2.dp)
                }
            }

        }
    }
}