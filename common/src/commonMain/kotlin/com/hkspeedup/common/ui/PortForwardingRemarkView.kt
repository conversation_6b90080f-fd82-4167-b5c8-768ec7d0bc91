package com.hkspeedup.common.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.service.FlyShadowApiService
import com.hkspeedup.common.service.config
import com.hkspeedup.common.ui.component.BackNavBar
import com.hkspeedup.common.ui.component.CustomTextField
import com.hkspeedup.common.util.Base64Util
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.viewmodel.ViewModel
import com.hkspeedup.common.vo.ClientVo
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.error
import flyshadow.common.generated.resources.remark
import flyshadow.common.generated.resources.save
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource


object PortForwardingRemarkView {
    var remark: String = ""
}

@Composable
fun PortForwardingRemarkView(viewModel: ViewModel) {
    Box(
        Modifier.padding(10.dp).background(MaterialTheme.colors.surface, shape = RoundedCornerShape(10.dp))
    ) {
        var remark by remember { mutableStateOf(PortForwardingRemarkView.remark) }

        // 自定义规则列表标题
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            BackNavBar(stringResource(Res.string.remark)) {
                TextButton(onClick = {
                    ViewFunction.setWindowLoading(true)
                    GlobalScope.launch(Dispatchers.IO) {
                        kotlin.runCatching {
                            val result =
                                FlyShadowApiService.createApi(viewModel.proxyStatus == ProxyStatus.START)
                                    .clientUpload(
                                        Base64Util.encode(config.subscribePassword),
                                        ClientVo(config.uuid, remark)
                                    )
                            if (result.code == 200) {
                                ViewFunction.backNavigationStackPage()
                            } else {
                                ViewFunction.showAlertDialog(
                                    AlertDialogMessage(
                                        StringContentVo(
                                            null,
                                            result.message
                                        )
                                    )
                                )
                            }
                        }.onFailure {
                            GlobalLog.error(it, "设置客户端备注失败")
                            ViewFunction.showAlertDialog(
                                AlertDialogMessage(
                                    StringContentVo(
                                        Res.string.error,
                                        it.message
                                    )
                                )
                            )
                        }
                        ViewFunction.setWindowLoading(false)
                    }
                }, modifier = Modifier.enableFocusable()) {
                    Text(stringResource(Res.string.save))
                }
            }
            Divider(Modifier.padding(start = 10.dp))


            CustomTextField(
                value = remark,
                onValueChange = {
                    remark = it
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .enableFocusable()
                    .padding(horizontal = 15.dp, vertical = 5.dp)
                    .background(MaterialTheme.colors.surface)
                    .height(30.dp),
                singleLine = true,
                textStyle = TextStyle(fontSize = 12.sp),
                contentPadding = PaddingValues(5.dp)
            )
        }

    }

}