package com.hkspeedup.common.ui

import androidx.compose.runtime.Composable
import com.hkspeedup.common.viewmodel.ViewModel
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.onlySupportWin10
import org.jetbrains.compose.resources.stringResource

var OnSupportWin10Str: String = "Only supports Windows 10 and above"

@Composable
expect fun InitMainUiExpect(viewModel: ViewModel)

@Composable
fun InitMainUi(viewModel: ViewModel) {
    OnSupportWin10Str = stringResource(Res.string.onlySupportWin10)
    InitMainUiExpect(viewModel)
}