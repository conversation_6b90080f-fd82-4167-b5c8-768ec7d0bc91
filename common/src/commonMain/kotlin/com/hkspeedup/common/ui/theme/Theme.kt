package com.hkspeedup.common.ui.theme

import androidx.compose.material.MaterialTheme
import androidx.compose.material.SwitchDefaults
import androidx.compose.material.darkColors
import androidx.compose.material.lightColors
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

val lightThemeColor = lightColors(
    primary = Color(0xffe98f36),
    primaryVariant = Color(0xff868e96),
    secondary = Color(0xff5B39F2),
    secondaryVariant = Color(0xff4827C7),
    background = Color(0xffe9ecef),
    onPrimary = Color(0xff343a40),
    onSecondary = Color(0xffffffff),
    onSurface = Color(0xff495057)
)

val darkThemeColor = darkColors(
    primary = Color(0xffe98f36),
    primaryVariant = Color(0xff6c757d),
    secondary = Color(0xff7B5BF7),
    secondaryVariant = Color(0xff5B39F2),
    background = Color(0xff212529),
    surface = Color(0xff343a40),
    onPrimary = Color(0xfff8f9fa),
    onSecondary = Color(0xfff8f9fa),
    onBackground = Color(0xfff8f9fa),
    onSurface = Color(0xfff8f9fa)
)

// 通用的Switch颜色配置
@Composable
fun appSwitchColors() = SwitchDefaults.colors(
    checkedTrackColor = Color(0xFFFFA500),
    checkedTrackAlpha = 1f,
    checkedThumbColor = MaterialTheme.colors.surface
)

