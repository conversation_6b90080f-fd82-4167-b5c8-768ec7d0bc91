package com.hkspeedup.common.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.service.SubscribeService
import com.hkspeedup.common.service.config
import com.hkspeedup.common.ui.component.BackNavBar
import com.hkspeedup.common.util.importJsonSubscribeFile
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.viewmodel.ViewModel
import flyshadow.common.generated.resources.*
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource

@Composable
fun SetSubscribeLinkView(viewModel: ViewModel) {
    Box(
        Modifier.padding(10.dp)
            .background(MaterialTheme.colors.surface, shape = RoundedCornerShape(10.dp))
    ) {
        var subscribeUrl by remember { mutableStateOf(config.subscribePassword) }
        // 选择的文件路径
        var selectedFilePath by remember { mutableStateOf<String?>(null) }
        // 获取国际化字符串
        val selectingFileText = stringResource(Res.string.selectingFile)
        Column {
            BackNavBar(stringResource(Res.string.setSubscribe))

            Divider(Modifier.padding(start = 10.dp))

            Column(Modifier.fillMaxWidth().padding(top = 10.dp)) {
                Text(
                    stringResource(Res.string.subscribePassword),
                    fontSize = 15.sp,
                    lineHeight = 15.sp,
                    color = MaterialTheme.colors.onSurface,
                    modifier = Modifier.padding(start = 15.dp)
                )
                TextField(
                    value = subscribeUrl,
                    onValueChange = { subscribeUrl = it },
                    leadingIcon = {
                        Image(
                            painterResource(Res.drawable.url),
                            contentDescription = "",
                            Modifier.size(20.dp)
                        )
                    },
                    placeholder = { Text("Password", fontSize = 15.sp, lineHeight = 15.sp) },
                    maxLines = 3,
                    modifier = Modifier.fillMaxWidth().padding(horizontal = 10.dp).clip(RoundedCornerShape(10.dp))
                        .enableFocusable(),
                    textStyle = TextStyle(fontSize = 15.sp),
                    colors = TextFieldDefaults.textFieldColors(
                        backgroundColor = MaterialTheme.colors.surface,
                        textColor = MaterialTheme.colors.onSurface,
                        cursorColor = MaterialTheme.colors.secondary,
                        focusedIndicatorColor = MaterialTheme.colors.secondary,
                        placeholderColor = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                )

                Row(Modifier.align(alignment = Alignment.End)) {
                    // 更新订阅按钮
                    Button(
                        modifier = Modifier.padding(end = 10.dp, bottom = 5.dp).enableFocusable(),
                        onClick = {
                            ViewFunction.setWindowLoading(true)
                            SubscribeService.setSubscribeUrl(subscribeUrl, viewModel)
                        }) {
                        Text(stringResource(Res.string.updateAndSave))
                    }
                }

                Divider(Modifier.padding(10.dp))

                Text(
                    stringResource(Res.string.importSubscribeFile),
                    fontSize = 15.sp,
                    lineHeight = 15.sp,
                    color = MaterialTheme.colors.onSurface,
                    modifier = Modifier.padding(start = 15.dp)
                )

                // 文件选择区域
                Row(
                    modifier = Modifier.fillMaxWidth().padding(horizontal = 10.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Button(
                        onClick = {
                            // 导入订阅文件
                            importJsonSubscribeFile()
                        },
                        modifier = Modifier.enableFocusable()
                    ) {
                        Text(stringResource(Res.string.selectFile))
                    }
                }
            }
        }
    }

}