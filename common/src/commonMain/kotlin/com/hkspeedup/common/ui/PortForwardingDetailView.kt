package com.hkspeedup.common.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.service.FlyShadowApiService
import com.hkspeedup.common.service.PortForwardingService
import com.hkspeedup.common.service.config
import com.hkspeedup.common.ui.component.BackNavBar
import com.hkspeedup.common.ui.component.CustomTextField
import com.hkspeedup.common.ui.theme.appSwitchColors
import com.hkspeedup.common.util.Base64Util
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.viewmodel.ViewModel
import com.hkspeedup.common.vo.ClientVo
import com.hkspeedup.common.vo.PortForwardingVo
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.*
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource

object PortForwardingDetailView {
    var portForwardingVo: PortForwardingVo? = null
}

@OptIn(DelicateCoroutinesApi::class)
@Composable
fun PortForwardingDetailView(viewModel: ViewModel) {

    val uuid by remember { mutableStateOf(config.uuid) }
    var remark: String? by remember { mutableStateOf("") }

    var targetAddr by remember {
        mutableStateOf(
            TextFieldValue(
                PortForwardingDetailView.portForwardingVo?.targetAddr ?: ""
            )
        )
    }
    var targetUuid by remember { mutableStateOf(PortForwardingDetailView.portForwardingVo?.targetUuid ?: "") }
    var listenPort by remember { mutableStateOf(PortForwardingDetailView.portForwardingVo?.listenPort ?: 0) }
    var enable by remember { mutableStateOf(PortForwardingDetailView.portForwardingVo?.enable ?: true) }

    val clientList = remember { mutableStateListOf<ClientVo>() }
    var clientListSelect by remember {
        mutableStateOf(
            PortForwardingDetailView.portForwardingVo?.let { portForwarding ->
                clientList.indexOfFirst { portForwarding.targetUuid == it.uuid }.takeIf { it >= 0 } ?: 0
            } ?: 0
        )
    }

    DisposableEffect(Unit) {
        val job = GlobalScope.launch(Dispatchers.IO) {
            ViewFunction.setWindowLoading(true)
            kotlin.runCatching {
                PortForwardingService.getClientList(config.subscribePassword, viewModel)?.let { it ->
                    clientList.clear()
                    clientList.addAll(it.filter {
                        val b = it.uuid != config.uuid
                        if (!b) {
                            remark = it.remark
                        }
                        b
                    })
                    if (clientList.isNotEmpty()) {
                        clientListSelect = PortForwardingDetailView.portForwardingVo?.let { portForwarding ->
                            clientList.indexOfFirst { portForwarding.targetUuid == it.uuid }.takeIf { it >= 0 } ?: 0
                        } ?: 0
                        targetUuid = clientList[clientListSelect].uuid
                    }
                }
            }.onFailure {
                GlobalLog.error(it, "获取端口转发信息失败")
                if (it.message?.contains("401") == true) {
                    ViewFunction.showAlertDialog(AlertDialogMessage(StringContentVo(Res.string.loginFailure, null)))
                }
            }

            ViewFunction.setWindowLoading(false)
        }
        // 使用 DisposableEffect 来清理资源或取消工作
        onDispose {
            ViewFunction.setWindowLoading(false)
            job.cancel() // 在界面消失时取消协程
        }
    }

    Box(
        Modifier.padding(10.dp)
    ) {
        // 编辑自定义规则标题
        Column {
            Row(Modifier.background(MaterialTheme.colors.surface, shape = RoundedCornerShape(10.dp))) {
                BackNavBar(stringResource(Res.string.portForwarding)) {
                    PortForwardingDetailView.portForwardingVo?.id?.let {
                        // 删除
                        TextButton(onClick = {
                            GlobalScope.launch(Dispatchers.IO) {
                                ViewFunction.setWindowLoading(true)
                                kotlin.runCatching {
                                    val result =
                                        FlyShadowApiService.createApi(viewModel.proxyStatus == ProxyStatus.START)
                                            .deletePortForwarding(
                                                Base64Util.encode(config.subscribePassword),
                                                it
                                            )
                                    if (result.code == 200) {
                                        ViewFunction.backNavigationStackPage()
                                    } else {
                                        ViewFunction.showAlertDialog(
                                            AlertDialogMessage(
                                                StringContentVo(
                                                    null,
                                                    result.message
                                                )
                                            )
                                        )
                                    }
                                }.onFailure {
                                    GlobalLog.error(it, "删除端口转发数据失败")
                                    if (it.message?.contains("401") == true) {
                                        ViewFunction.showAlertDialog(
                                            AlertDialogMessage(
                                                StringContentVo(
                                                    Res.string.loginFailure,
                                                    null
                                                )
                                            )
                                        )
                                    }
                                }
                                ViewFunction.setWindowLoading(false)
                            }
                        }, modifier = Modifier.enableFocusable()) {
                            Text(stringResource(Res.string.delete))
                        }

                    }

                    // 保存
                    TextButton(onClick = {
                        GlobalScope.launch(Dispatchers.IO) {
                            ViewFunction.setWindowLoading(true)
                            kotlin.runCatching {
                                val updateCustomRule =
                                    FlyShadowApiService.createApi(viewModel.proxyStatus == ProxyStatus.START)
                                        .portForwardingSaveOrUpdate(
                                            Base64Util.encode(config.subscribePassword),
                                            PortForwardingVo(
                                                PortForwardingDetailView.portForwardingVo?.id,
                                                uuid,
                                                listenPort,
                                                targetAddr.text.takeIf { it.contains(":") } ?: "${targetAddr.text}:80",
                                                targetUuid,
                                                enable,
                                                null
                                            )
                                        )
                                if (updateCustomRule.code == 200) {
                                    ViewFunction.backNavigationStackPage()
                                } else {
                                    ViewFunction.showAlertDialog(
                                        AlertDialogMessage(
                                            StringContentVo(
                                                null,
                                                updateCustomRule.message
                                            )
                                        )
                                    )
                                }
                            }.onFailure {
                                GlobalLog.error(it, "更新自定义规则列表失败")
                                if (it.message?.contains("401") == true) {
                                    ViewFunction.showAlertDialog(
                                        AlertDialogMessage(
                                            StringContentVo(
                                                Res.string.loginFailure,
                                                null
                                            )
                                        )
                                    )
                                }
                            }
                            ViewFunction.setWindowLoading(false)
                        }
                    }, modifier = Modifier.enableFocusable()) {
                        Text(stringResource(Res.string.save))
                    }
                }
            }

            // 客户端ID
            Text(
                "${stringResource(Res.string.client)} ID",
                modifier = Modifier.padding(top = 10.dp, bottom = 5.dp),
                fontSize = 11.sp,
                color = MaterialTheme.colors.onSurface
            )
            Row(Modifier.background(MaterialTheme.colors.surface, shape = RoundedCornerShape(5.dp)).fillMaxWidth()) {
                Text(
                    modifier = Modifier.padding(vertical = 10.dp, horizontal = 10.dp),
                    text = "$uuid ${remark?.takeIf { it.isNotBlank() }?.let { "($it)" } ?: ""}",
                    fontSize = 12.sp,
                    lineHeight = 12.sp,
                    color = MaterialTheme.colors.onSurface
                )
            }

            // 监听端口
            Text(
                stringResource(Res.string.listenPort),
                modifier = Modifier.padding(top = 10.dp, bottom = 5.dp),
                fontSize = 11.sp,
                color = MaterialTheme.colors.onSurface
            )
            Row(Modifier.background(MaterialTheme.colors.surface, shape = RoundedCornerShape(5.dp))) {
                CustomTextField(
                    value = listenPort.toString(),
                    onValueChange = {
                        if (it.isNotEmpty() && (it.all { char -> char.isDigit() } && it.toLong() >= 0 && it.toLong() <= 65535)) {
                            listenPort = it.toInt()
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .enableFocusable()
                        .padding(horizontal = 15.dp, vertical = 5.dp)
                        .background(MaterialTheme.colors.surface)
                        .height(30.dp),
                    singleLine = true,
                    textStyle = TextStyle(fontSize = 12.sp),
                    contentPadding = PaddingValues(5.dp)
                )
            }


            // 目标地址
            Text(
                stringResource(Res.string.targetAddress),
                modifier = Modifier.padding(top = 10.dp, bottom = 5.dp),
                fontSize = 11.sp,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
            )
            Row(Modifier.background(MaterialTheme.colors.surface, shape = RoundedCornerShape(5.dp))) {
                CustomTextField(
                    value = targetAddr,
                    onValueChange = {
                        targetAddr = it
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .enableFocusable()
                        .padding(horizontal = 15.dp, vertical = 5.dp)
                        .background(MaterialTheme.colors.surface)
                        .height(30.dp),
                    singleLine = true,
                    textStyle = TextStyle(fontSize = 12.sp),
                    contentPadding = PaddingValues(5.dp)
                )
            }


            Spacer(modifier = Modifier.padding(10.dp))
            // 目标客户端
            Text(
                stringResource(Res.string.targetClient),
                modifier = Modifier.padding(top = 10.dp, bottom = 5.dp),
                fontSize = 11.sp,
                color = MaterialTheme.colors.onSurface
            )
            Row(
                Modifier
                    .background(MaterialTheme.colors.surface, shape = RoundedCornerShape(5.dp))
                    .height(40.dp)
                    .fillMaxWidth()
                    .padding(horizontal = 15.dp, vertical = 5.dp)
            ) {
                if (clientList.isEmpty()) {
                    Text(
                        stringResource(Res.string.noClientToChoose),
                        fontSize = 11.sp,
                        lineHeight = 11.sp,
                        color = MaterialTheme.colors.onSurface
                    )
                } else {
                    DropdownMenu(
                        selectedIndex = clientListSelect,
                        onSelectedChange = {
                            clientListSelect = it
                            targetUuid = clientList[it].uuid
                        },
                        list = clientList.filter { it.uuid != config.uuid },
                    )
                }
            }

            Spacer(modifier = Modifier.padding(10.dp))
            // 启用开关
            Text(
                stringResource(Res.string.enableOrNot),
                modifier = Modifier.padding(top = 10.dp, bottom = 5.dp),
                fontSize = 11.sp,
                color = MaterialTheme.colors.onSurface
            )
            Row(
                modifier = Modifier
                    .background(MaterialTheme.colors.surface, shape = RoundedCornerShape(5.dp))
                    .height(40.dp)
                    .padding(horizontal = 15.dp, vertical = 5.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    stringResource(Res.string.enableOrNot),
                    Modifier.weight(1f).padding(0.dp),
                    fontSize = 13.sp,
                    lineHeight = 13.sp,
                    color = MaterialTheme.colors.onSurface
                )
                Switch(
                    modifier = Modifier.enableFocusable(),
                    checked = enable,
                    onCheckedChange = { enable = it },
                    colors = appSwitchColors()
                )
            }
        }
    }
}


@OptIn(ExperimentalMaterialApi::class)
@Composable
fun DropdownMenu(
    selectedIndex: Int,
    onSelectedChange: (Int) -> Unit,
    list: List<ClientVo>,
) {
    var expanded by remember { mutableStateOf(false) }

    ExposedDropdownMenuBox(
        modifier = Modifier
            .enableFocusable()
            .background(MaterialTheme.colors.surface),
        expanded = expanded,
        onExpandedChange = { expanded = !expanded }
    ) {
        CustomTextField(
            value = list[selectedIndex].uuid,
            onValueChange = {},
            label = {
                Text(
                    color = MaterialTheme.colors.onSurface,
                    text = "${list[selectedIndex].uuid} ${
                        list[selectedIndex].remark?.takeIf { it.isNotBlank() }?.let { "($it)" } ?: ""
                    }",
                    fontSize = 12.sp,
                    lineHeight = 12.sp,
                    modifier = Modifier.padding(0.dp)
                )
            },
            readOnly = true,
            trailingIcon = {
                ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
            },
            textStyle = TextStyle(fontSize = 12.sp, lineHeight = 12.sp),
            modifier = Modifier.fillMaxWidth(),
        )
        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            list.forEachIndexed { index, clientVo ->
                DropdownMenuItem(
                    onClick = {
                        onSelectedChange(index)
                        expanded = false
                    }
                ) {
                    Column {
                        Text(
                            clientVo.uuid,
                            color = MaterialTheme.colors.onSurface,
                            fontSize = 12.sp,
                            lineHeight = 12.sp,
                            modifier = Modifier.padding(0.dp)
                        )
                        clientVo.remark?.takeIf { it.isNotBlank() }?.let {
                            Text(
                                "${stringResource(Res.string.remark)}: $it",
                                fontSize = 11.sp,
                                color = MaterialTheme.colors.onSurface,
                                lineHeight = 11.sp,
                                modifier = Modifier.padding(0.dp)
                            )
                        }
                    }

                }
            }
        }
    }
}