package com.hkspeedup.common.ui.component

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.material.MaterialTheme
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hkspeedup.common.entension.enableFocusable


@OptIn(ExperimentalMaterialApi::class)
@Composable
fun DropdownMenu(
    modifier: Modifier = Modifier,
    selectedIndex: Int,
    onSelectedChange: (Int) -> Unit,
    list: List<String>,
) {
    var expanded by remember { mutableStateOf(false) }

    ExposedDropdownMenuBox(
        modifier = Modifier
            .enableFocusable()
            .background(MaterialTheme.colors.surface),
        expanded = expanded,
        onExpandedChange = { expanded = !expanded }
    ) {
        CustomTextField(
            value = list[selectedIndex],
            onValueChange = {},
            label = {
                Text(
                    color = MaterialTheme.colors.onSurface,
                    text = list[selectedIndex],
                    fontSize = 12.sp,
                    lineHeight = 12.sp,
                    modifier = Modifier.padding(0.dp)
                )
            },
            readOnly = true,
            trailingIcon = {
                ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded)
            },
            textStyle = TextStyle(fontSize = 12.sp, lineHeight = 12.sp),
            modifier = Modifier.fillMaxWidth(),
        )
        ExposedDropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            list.forEachIndexed { index, proxyRule ->
                DropdownMenuItem(
                    onClick = {
                        onSelectedChange(index)
                        expanded = false
                    }
                ) {
                    Text(
                        proxyRule,
                        fontSize = 12.sp,
                        lineHeight = 12.sp,
                        color = MaterialTheme.colors.onSurface,
                        modifier = Modifier.padding(0.dp)
                    )
                }
            }
        }
    }
}