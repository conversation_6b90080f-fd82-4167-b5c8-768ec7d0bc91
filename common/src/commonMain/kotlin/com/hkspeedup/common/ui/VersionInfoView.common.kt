package com.hkspeedup.common.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Divider
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.hutool.core.util.StrUtil
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.ui.component.BackNavBar
import com.hkspeedup.common.util.GradleProperties
import com.hkspeedup.common.util.VersionChecker
import com.hkspeedup.common.viewmodel.ViewModel
import flyshadow.common.generated.resources.*
import kotlinx.coroutines.*
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource

@Composable
fun VersionInfoView(viewModel: ViewModel) {
    Box(
        Modifier.padding(10.dp)
            .background(MaterialTheme.colors.surface, shape = RoundedCornerShape(10.dp))
    ) {
        Column {
            BackNavBar(stringResource(Res.string.versionDetail))
            CurrentVersion(viewModel)
            DownloadFile()
            OpenAppFolder()
        }
    }
}

@Composable
expect fun OpenAppFolder()

@Composable
fun DownloadFile() {
    Divider(Modifier.padding(start = 10.dp))
    var downloadFileSize by remember { mutableStateOf(VersionChecker.getDownloadFileSize()) }
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth()
            .height(35.dp)
            .clip(RoundedCornerShape(10.dp))
            .enableFocusable()
            .clickable {
                VersionChecker.cleanDownloadFile()
                downloadFileSize = VersionChecker.getDownloadFileSize()
            }
    ) {
        Row(
            Modifier.weight(1f).padding(start = 10.dp, end = 20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.clean),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.cleanDownloadCache),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                color = MaterialTheme.colors.onSurface,
                modifier = Modifier.padding(start = 5.dp).weight(1f)
            )
            Text(
                downloadFileSize,
                fontSize = 13.sp,
                lineHeight = 13.sp,
                color = MaterialTheme.colors.onSurface
            )
        }
    }
}

@OptIn(DelicateCoroutinesApi::class)
@Composable
fun CurrentVersion(viewModel: ViewModel) {
    Divider(Modifier.padding(start = 10.dp))
    var isCheckVersion by remember { mutableStateOf(true) }
    var checkVersionJob: Job? = if (viewModel.downloadProcess == 0f) {
        GlobalScope.launch(Dispatchers.IO) {
            VersionChecker.checkForUpdate(viewModel)
            isCheckVersion = false
        }
    } else {
        null
    }

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth()
            .height(viewModel.versionDesc?.let { 50.dp } ?: 35.dp)
            .clip(RoundedCornerShape(10.dp))
            .enableFocusable()
            .clickable {
                checkVersionJob?.cancel()
                if (StrUtil.isBlank(viewModel.version)) {
                    isCheckVersion = true
                    checkVersionJob = GlobalScope.launch(Dispatchers.IO) {
                        VersionChecker.checkForUpdate(viewModel)
                        isCheckVersion = false
                    }
                } else {
                    if (viewModel.downloadProcess > 0 && viewModel.downloadProcess < 100) {
                        VersionChecker.cancelDownload(viewModel)
                    } else {
                        VersionChecker.downloadPackageFile(viewModel)
                    }
                }
            }
    ) {
        Row(
            Modifier.weight(1f).padding(start = 10.dp, end = 20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.version),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Column(modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)) {
                Text(
                    "${stringResource(Res.string.version)}${
                        viewModel.version?.let {
                            if (viewModel.downloadProcess > 0 && viewModel.downloadProcess < 100)
                                "(${stringResource(Res.string.clickToCancel)})"
                            else "(${stringResource(Res.string.clickToDownload)})"
                        } ?: ""
                    }",
                    fontSize = 13.sp,
                    lineHeight = 1.sp,
                    color = MaterialTheme.colors.onSurface
                )
                viewModel.versionDesc?.let {
                    Text(
                        it,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                        fontSize = 12.sp,
                        lineHeight = 12.sp
                    )
                }
            }
            if ((viewModel.downloadProcess > 0 && viewModel.downloadProcess < 100) || isCheckVersion) {
                CircularProgressIndicator(modifier = Modifier.size(15.dp), strokeWidth = 2.dp)
            }
            Row(Modifier.padding(start = 5.dp)) {
                Text(
                    "v${GradleProperties.getVersion()}",
                    fontSize = 13.sp,
                    lineHeight = 13.sp,
                    color = MaterialTheme.colors.onSurface
                )
                viewModel.version?.let {
                    Text("($it)", fontSize = 13.sp, lineHeight = 13.sp, color = MaterialTheme.colors.error)
                }
            }
        }
        if (viewModel.downloadProcess > 0 && viewModel.downloadProcess < 100) {
            LinearProgressIndicator(progress = viewModel.downloadProcess, modifier = Modifier.height(2.dp))
        }
    }
}