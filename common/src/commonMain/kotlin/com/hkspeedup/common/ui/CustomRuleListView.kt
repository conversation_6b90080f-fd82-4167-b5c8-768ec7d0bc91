package com.hkspeedup.common.ui

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.hutool.core.util.StrUtil
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.service.ConfigService
import com.hkspeedup.common.service.FlyShadowApiService
import com.hkspeedup.common.service.config
import com.hkspeedup.common.ui.component.BackNavBar
import com.hkspeedup.common.ui.component.CustomOutlinedTextField
import com.hkspeedup.common.ui.component.MyVerticalScrollbar
import com.hkspeedup.common.util.Base64Util
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.viewmodel.*
import com.hkspeedup.common.vo.CustomRuleVo
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.*
import kotlinx.coroutines.*
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import sh.calvin.reorderable.ReorderableItem
import sh.calvin.reorderable.rememberReorderableLazyListState

@OptIn(DelicateCoroutinesApi::class, ExperimentalFoundationApi::class)
@Composable
fun CustomRuleListView(viewModel: ViewModel) {
    var ruleList = remember { mutableStateListOf<CustomRuleVo>() }
    var searchText by remember { mutableStateOf(TextFieldValue()) }

    DisposableEffect(Unit) {
        val job = GlobalScope.launch(Dispatchers.IO) {
            ViewFunction.setWindowLoading(true)
            kotlin.runCatching {
                val customRuleList =
                    FlyShadowApiService.createApi(viewModel.proxyStatus == ProxyStatus.START).getCustomRuleList(
                        Base64Util.encode(config.subscribePassword)
                    )
                customRuleList.data?.let {
                    config.customRuleList = customRuleList.data
                    ConfigService.save()
                    withContext(Dispatchers.Main) {
                        ruleList.addAll(it)
                    }
                }
            }.onFailure {
                GlobalLog.error(it, "获取自定义规则列表失败")
                if (it.message?.contains("401") == true) {
                    ViewFunction.showAlertDialog(AlertDialogMessage(StringContentVo(Res.string.loginFailure, null)))
                }
            }
            ViewFunction.setWindowLoading(false)
        }
        // 使用 DisposableEffect 来清理资源或取消工作
        onDispose {
            ViewFunction.setWindowLoading(false)
            job.cancel() // 在界面消失时取消协程
        }
    }

    Box(
        Modifier.padding(10.dp).background(MaterialTheme.colors.surface, shape = RoundedCornerShape(10.dp))
    ) {
        val state = rememberLazyListState()

        // 自定义规则列表标题
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            BackNavBar(stringResource(Res.string.customRuleList)) {
                TextButton(onClick = {
                    CustomRuleDetailView.customRuleVo = null
                    ViewFunction.pushNavigationStackPage(CustomRuleDetailPage())
                }, modifier = Modifier.enableFocusable()) {
                    Text(stringResource(Res.string.create))
                }
            }

            Divider(Modifier.padding(start = 10.dp))

            CustomOutlinedTextField(
                value = searchText,
                onValueChange = { searchText = it },
                label = { Text(stringResource(Res.string.searchContent), modifier = Modifier.padding(0.dp)) },
                placeholder = { Text(stringResource(Res.string.searchContent), modifier = Modifier.padding(0.dp)) },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(10.dp)
                    .enableFocusable()
                    .height(50.dp),
                singleLine = true, // 确保输入框为单行
                contentPadding = PaddingValues(5.dp)
            )

            if (ruleList.isEmpty()) {
                return
            }

            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                val reorderableLazyListState = rememberReorderableLazyListState(state) { from, to ->
                    ruleList = ruleList.apply {
                        add(to.index, removeAt(from.index))
                    }
                }

                val highLineIndex = hashSetOf<Int>()
                val textSplit = searchText.text.lowercase().split(" ")
                ruleList.forEachIndexed { index, customRuleVo ->
                    var result = false
                    for (s in textSplit) {
                        if (StrUtil.isBlank(s)) {
                            continue
                        }
                        result = result || customRuleVo.domain.lowercase().contains(s)
                    }
                    if (result) {
                        highLineIndex.add(index)
                    }
                }

                if (highLineIndex.isNotEmpty()) {
                    LaunchedEffect(Unit) {
                        state.scrollToItem(highLineIndex.last())
                    }
                }

                // 连接列表
                LazyColumn(state = state, modifier = Modifier.padding(start = 5.dp, end = 5.dp)) {
                    items(ruleList.size, key = { ruleList[it].id!! }) { index ->
                        val rule = ruleList[index]
                        ReorderableItem(
                            reorderableLazyListState,
                            key = rule.id!!,
                            Modifier.fillMaxWidth().enableFocusable()
                        ) { isDragging ->
                            val elevation by animateDpAsState(if (isDragging) 4.dp else 0.dp)
                            Surface(elevation = elevation) {
                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    // 内容
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier.weight(1f).enableFocusable()
                                            .background(
                                                if (highLineIndex.contains(index)) MaterialTheme.colors.primary.copy(
                                                    alpha = 0.2f
                                                ) else MaterialTheme.colors.surface
                                            )
                                            .clickable {
                                                // 编辑点击事件
                                                CustomRuleDetailView.customRuleVo = rule.copy()
                                                ViewFunction.pushNavigationStackPage(CustomRuleDetailPage())
                                            },
                                    ) {
                                        Column(
                                            Modifier.weight(1f).padding(10.dp),
                                            verticalArrangement = Arrangement.Center
                                        ) {

                                            Text(
                                                rule.domain, fontSize = 13.sp, lineHeight = 13.sp, color = MaterialTheme.colors.onSurface,
                                                style = TextStyle(textDecoration = if (rule.enable == 1) null else TextDecoration.LineThrough)
                                            )

                                            val matching = when (rule.matching) {
                                                0 -> stringResource(Res.string.domainNameMatch)
                                                1 -> stringResource(Res.string.domainNameSuffixMatch)
                                                2 -> stringResource(Res.string.domainNameKeywordMatch)
                                                3 -> stringResource(Res.string.CIDR4Match)
                                                4 -> stringResource(Res.string.CIDR6Match)
                                                5 -> stringResource(Res.string.srcCIDR4Match)
                                                6 -> stringResource(Res.string.geoIpMatch)
                                                7 -> stringResource(Res.string.dstPortMatch)
                                                8 -> stringResource(Res.string.srcPortMatch)
                                                9 -> stringResource(Res.string.processNameMatch)
                                                10 -> stringResource(Res.string.allMatch)
                                                11 -> stringResource(Res.string.clashClassicalMatch)
                                                12 -> stringResource(Res.string.clashDomainMatch)
                                                else -> null
                                            }

                                            // 规则
                                            matching?.let {
                                                Text(
                                                    it,
                                                    fontSize = 10.sp,
                                                    lineHeight = 10.sp,
                                                    color = Color.Gray
                                                )
                                            }
                                        }

                                        // 直连优先
                                        if (rule.directConnPriority != 0) {
                                            Text(
                                                modifier = Modifier.padding(end = 5.dp),
                                                text = "(${stringResource(Res.string.directConnPriority)})",
                                                fontSize = 10.sp,
                                                lineHeight = 10.sp,
                                                color = Color.Gray
                                            )
                                        }

                                        // 代理类型
                                        val proxyType = when (rule.proxyType) {
                                            0 -> stringResource(Res.string.direct)
                                            1 -> stringResource(Res.string.reject)
                                            2 -> stringResource(Res.string.proxy)
                                            else -> ""
                                        }
                                        Text(
                                            proxyType, fontSize = 10.sp, lineHeight = 10.sp, color = Color.Gray
                                        )

                                        Image(
                                            painter = painterResource(Res.drawable.right),
                                            contentDescription = "",
                                            modifier = Modifier.size(15.dp),
                                        )

                                    }
                                    // 拖拽图片
                                    Image(
                                        painter = painterResource(Res.drawable.drag),
                                        contentDescription = "",
                                        modifier = Modifier
                                            .size(20.dp)
                                            .enableFocusable()
                                            .draggableHandle(onDragStopped = {
                                                // 停止拖拽后判断位置排序
                                                val updateList = ruleList.filterIndexed { index, customRuleVo ->
                                                    val b = index != customRuleVo.sort
                                                    customRuleVo.sort = index
                                                    b
                                                }
                                                if (updateList.isEmpty()) {
                                                    return@draggableHandle
                                                }
                                                GlobalScope.launch(Dispatchers.IO) {
                                                    ViewFunction.setWindowLoading(true)
                                                    kotlin.runCatching {
                                                        FlyShadowApiService.createApi(viewModel.proxyStatus == ProxyStatus.START)
                                                            .updateCustomRuleList(
                                                                Base64Util.encode(config.subscribePassword),
                                                                updateList
                                                            )
                                                    }.onFailure {
                                                        GlobalLog.error(it, "更新自定义规则列表失败")
                                                        if (it.message?.contains("401") == true) {
                                                            ViewFunction.showAlertDialog(
                                                                AlertDialogMessage(
                                                                    StringContentVo(
                                                                        Res.string.loginFailure,
                                                                        null
                                                                    )
                                                                )
                                                            )
                                                        }
                                                    }
                                                    ViewFunction.setWindowLoading(false)
                                                }
                                            }),
                                    )
                                }
                            }

                            Divider(Modifier.fillMaxWidth().height(1.dp))

                        }
                    }
                }
                MyVerticalScrollbar(
                    modifier = Modifier.align(Alignment.CenterEnd), state = state
                )
            }
        }

    }

}