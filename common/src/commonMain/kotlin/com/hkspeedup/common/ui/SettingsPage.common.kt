package com.hkspeedup.common.ui

import androidx.compose.foundation.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.material.MaterialTheme
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.lib.FlyShadowCore
import com.hkspeedup.common.service.ConfigService
import com.hkspeedup.common.service.MacTunService
import com.hkspeedup.common.service.WinTunService
import com.hkspeedup.common.service.config
import com.hkspeedup.common.ui.component.CustomTextField
import com.hkspeedup.common.ui.component.MyVerticalScrollbar
import com.hkspeedup.common.ui.theme.appSwitchColors
import com.hkspeedup.common.util.*
import com.hkspeedup.common.viewmodel.*
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.*
import kotlinx.coroutines.*
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import java.util.*

@Composable
fun SettingPage(viewModel: ViewModel) {
    val state = rememberScrollState()
    Box(Modifier.fillMaxSize().padding(bottom = 70.dp)) {
        Column(
            Modifier.padding(20.dp).background(MaterialTheme.colors.surface, shape = RoundedCornerShape(10.dp)).verticalScroll(state)
        ) {
            LanguageSelector(viewModel)
            DarkModeSelector(viewModel)
            LocalProxyPort(viewModel)
            ConnectionList(viewModel)
            CustomRuleList(viewModel)
            PortForwardingList(viewModel)
            ApplicationList(viewModel)
            if (SystemUtil.isWindows()) {
                WinTunView(viewModel)
            } else if (SystemUtil.isMac()) {
                MacTunView(viewModel)
            }
            UseBuildInDnsView(viewModel)
            UdpProxyRuleView(viewModel)
            NativeUdpEnableView(viewModel)
            DirectConnPriorityEnableView(viewModel)
            if (SystemUtil.isWindows() || SystemUtil.isMac()) {
                AutoSetSysProxyOnStartUpView(viewModel)
            }
            if (SystemUtil.isWindows() || SystemUtil.isMac()) {
                StartupEnableView(viewModel)
            }
            LaunchOnStartupView(viewModel)
            MinimizeAfterStartupView(viewModel)
            AppVersion(viewModel)
        }

        MyVerticalScrollbar(
            modifier = Modifier.padding(top = 20.dp, bottom = 20.dp, end = 20.dp).align(Alignment.CenterEnd),
            state = state
        )
    }
}


@Composable
fun LanguageSelector(viewModel: ViewModel) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 20.dp).enableFocusable(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.language),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Column(modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)) {
                Text(stringResource(Res.string.language), fontSize = 13.sp, lineHeight = 1.sp)
            }

            var selectedTabIndex by remember { mutableStateOf(0) }

            if (config.currentLanguage == "ZH") {
                selectedTabIndex = 0
            }
            if (config.currentLanguage == "EN") {
                selectedTabIndex = 1
            }

            TabRow(
                backgroundColor = MaterialTheme.colors.surface,
                contentColor = MaterialTheme.colors.primary,
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.width(150.dp).clip(RoundedCornerShape(5.dp))
            ) {
                Tab(
                    modifier = Modifier.width(65.dp).height(25.dp).enableFocusable(),
                    selected = selectedTabIndex == 0,
                    onClick = {
                        selectedTabIndex = 0
                        config.currentLanguage = "ZH"
                        ConfigService.save()
                        Locale.setDefault(Locale(config.currentLanguage))
                        viewModel.language = "ZH"
                    }
                ) {
                    Text(
                        stringResource(Res.string.chinese), fontSize = 13.sp, lineHeight = 1.sp,
                        modifier = Modifier.height(20.dp)
                    )
                }
                Tab(
                    modifier = Modifier.width(45.dp).height(25.dp).enableFocusable(),
                    selected = selectedTabIndex == 1,
                    onClick = {
                        selectedTabIndex = 1
                        config.currentLanguage = "EN"
                        ConfigService.save()
                        Locale.setDefault(Locale(config.currentLanguage))
                        viewModel.language = "EN"
                    }
                ) {
                    Text(
                        stringResource(Res.string.english), fontSize = 13.sp, lineHeight = 1.sp,
                        modifier = Modifier.height(20.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun DarkModeSelector(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.dark_mode),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.darkMode),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
            )
            Switch(
                viewModel.darkMode,
                onCheckedChange = {
                    viewModel.darkMode = it
                    config.darkMode = it
                    ConfigService.save()
                },
                colors = appSwitchColors()
            )
        }
    }
}

@Composable
fun LocalProxyPort(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .enableFocusable()
            .clickable {
                ViewFunction.pushNavigationStackPage(LocalProxyPortPage())
            }
    ) {
        Row(
            Modifier.padding(horizontal = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.target),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Column(modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)) {
                Text(stringResource(Res.string.localProxyPort), fontSize = 13.sp, lineHeight = 1.sp)
                Text(
                    "http/https/socks5",
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f),
                    lineHeight = 1.sp,
                    modifier = Modifier.padding(end = 10.dp),
                    fontSize = 8.sp
                )
            }
            Text(config.proxyPort.toString(), fontSize = 13.sp, lineHeight = 1.sp)
            Image(
                painter = painterResource(Res.drawable.right),
                contentDescription = "", modifier = Modifier.size(15.dp),
                colorFilter = ColorFilter.tint(color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f))
            )
        }
    }
}


@Composable
fun ConnectionList(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .enableFocusable()
            .clickable {
                ViewFunction.pushNavigationStackPage(TunnelMapperInfoPage())
            }) {
        Row(
            Modifier.padding(horizontal = 10.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                painter = painterResource(Res.drawable.connections),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.connectList),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                color = MaterialTheme.colors.onSurface,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
            )
            Image(
                painter = painterResource(Res.drawable.right),
                contentDescription = "", modifier = Modifier.size(15.dp),
                colorFilter = ColorFilter.tint(color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f))
            )
        }
    }
}

@Composable
fun CustomRuleList(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    Column(
        Modifier.fillMaxWidth().enableFocusable()
            .clickable {
                ViewFunction.pushNavigationStackPage(CustomRuleListPage())
            }) {
        Box {
            Row(
                Modifier.padding(horizontal = 10.dp).height(35.dp).fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Image(
                    painter = painterResource(Res.drawable.rule),
                    contentDescription = stringResource(Res.string.customRuleList), modifier = Modifier.size(19.dp)
                )
                Text(
                    stringResource(Res.string.customRuleList),
                    fontSize = 13.sp,
                    lineHeight = 1.sp,
                    color = MaterialTheme.colors.onSurface,
                    modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
                )
                Image(
                    painter = painterResource(Res.drawable.right),
                    contentDescription = "", modifier = Modifier.size(15.dp),
                    colorFilter = ColorFilter.tint(color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f))
                )
            }

        }
    }
}

@Composable
fun PortForwardingList(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    Column(
        Modifier.fillMaxWidth().enableFocusable()
            .clickable {
                ViewFunction.pushNavigationStackPage(PortForwardingListPage())
            }) {
        Box {
            Row(
                Modifier.padding(horizontal = 10.dp).height(35.dp).fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Image(
                    painter = painterResource(Res.drawable.port_forwarding),
                    contentDescription = "", modifier = Modifier.size(19.dp)
                )
                Text(
                    stringResource(Res.string.portForwarding),
                    fontSize = 13.sp,
                    lineHeight = 1.sp,
                    color = MaterialTheme.colors.onSurface,
                    modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
                )
                Image(
                    painter = painterResource(Res.drawable.right),
                    contentDescription = "", modifier = Modifier.size(15.dp),
                    colorFilter = ColorFilter.tint(color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f))
                )
            }

        }
    }
}


@OptIn(DelicateCoroutinesApi::class)
@Composable
fun WinTunView(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    viewModel.tunEnable = config.tunModelEnable

    // TUN 网卡开关
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.network),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.tunMode),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
            )
            if (viewModel.tunOpening) {
                CircularProgressIndicator(modifier = Modifier.size(15.dp), strokeWidth = 2.dp)
            }
            Switch(
                viewModel.tunEnable, onCheckedChange = {
                    config.tunModelEnable = it
                    viewModel.tunEnable = it
                    ConfigService.save()
                    GlobalScope.launch(Dispatchers.IO) {
                        try {
                            viewModel.tunOpening = true
                            if (config.tunModelEnable && viewModel.proxyStatus != ProxyStatus.STOP) {
                                WinTunService.openTun()
                            } else {
                                WinTunService.stopTun()
                            }
                        } catch (e: Exception) {
                            GlobalLog.error(e, "Open win tun error")
                            ViewFunction.showAlertDialog(
                                AlertDialogMessage(
                                    StringContentVo(
                                        Res.string.startTunFail,
                                        e.message
                                    )
                                )
                            )
                            config.tunModelEnable = !it
                            viewModel.tunEnable = !it
                            ConfigService.save()
                        } finally {
                            viewModel.tunOpening = false
                        }
                    }
                },
                enabled = !viewModel.tunOpening,
                colors = appSwitchColors()
            )
        }
    }

    // IPv6 启用
    Divider(startIndent = 40.dp)
    var ipv6Enable by remember { mutableStateOf(config.ipv6Enable) }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 39.dp, end = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.ipv6),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.ipv6Enable),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
            )
            Switch(
                ipv6Enable,
                onCheckedChange = {
                    config.ipv6Enable = it
                    ipv6Enable = it
                    FlyShadowCore.setIPv6Enable(it)
                    ConfigService.save()
                },
                enabled = !viewModel.tunEnable,
                colors = appSwitchColors()
            )
        }
    }
}

@OptIn(DelicateCoroutinesApi::class)
@Composable
fun MacTunView(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    viewModel.tunEnable = config.tunModelEnable

    // TUN 网卡开关
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.network),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.tunMode),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
            )
            if (viewModel.tunOpening) {
                CircularProgressIndicator(modifier = Modifier.size(15.dp), strokeWidth = 2.dp)
            }
            Switch(
                viewModel.tunEnable, onCheckedChange = {
                    config.tunModelEnable = it
                    viewModel.tunEnable = it
                    ConfigService.save()
                    GlobalScope.launch(Dispatchers.IO) {
                        try {
                            viewModel.tunOpening = true
                            if (config.tunModelEnable && viewModel.proxyStatus != ProxyStatus.STOP) {
                                MacTunService.openTun()
                            }
                        } catch (e: Exception) {
                            GlobalLog.error(e, "Open win tun error")
                            ViewFunction.showAlertDialog(
                                AlertDialogMessage(
                                    StringContentVo(
                                        Res.string.startTunFail,
                                        e.message
                                    )
                                )
                            )
                            config.tunModelEnable = !it
                            viewModel.tunEnable = !it
                            ConfigService.save()
                        } finally {
                            viewModel.tunOpening = false
                        }
                    }
                },
                enabled = !viewModel.tunOpening,
                colors = appSwitchColors()
            )
        }
    }

    // IPv6 启用
    Divider(startIndent = 40.dp)
    var ipv6Enable by remember { mutableStateOf(config.ipv6Enable) }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 39.dp, end = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.ipv6),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.ipv6Enable),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
            )
            Switch(
                ipv6Enable,
                onCheckedChange = {
                    config.ipv6Enable = it
                    ipv6Enable = it
                    FlyShadowCore.setIPv6Enable(it)
                    ConfigService.save()
                },
                enabled = !viewModel.tunEnable,
                colors = appSwitchColors()
            )
        }
    }
}

@Composable
fun UseBuildInDnsView(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    var useBuildInDns by remember { mutableStateOf(config.useBuildInDns) }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.dns),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.buildInDns),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
            )
            Switch(
                useBuildInDns, onCheckedChange = {
                    config.useBuildInDns = it
                    useBuildInDns = it
                    FlyShadowCore.setUseBuildInDns(it)
                    ConfigService.save()
                },
                colors = appSwitchColors()
            )
        }
    }
}


@Composable
private fun UdpProxyRuleView(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    val redirectText = stringResource(Res.string.direct)
    val ruleText = stringResource(Res.string.rule)
    val globalText = stringResource(Res.string.global)
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 10.dp).enableFocusable(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.proxy),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                "UDP ${stringResource(Res.string.proxyMode)}",
                fontSize = 13.sp,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f),
                lineHeight = 1.sp
            )

            var selectedTabIndex by remember {
                mutableStateOf(
                    when (config.udpProxyType) {
                        0 -> 0
                        2 -> 1
                        1 -> 2
                        else -> 1
                    }
                )
            }

            TabRow(
                backgroundColor = MaterialTheme.colors.surface,
                contentColor = MaterialTheme.colors.primary,
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.width(155.dp).clip(RoundedCornerShape(5.dp))
            ) {
                Tab(
                    modifier = Modifier.width(70.dp).height(25.dp).enableFocusable(),
                    selected = selectedTabIndex == 0,
                    onClick = {
                        FlyShadowCore.setUdpProxyType(0)
                        config.udpProxyType = 0
                        selectedTabIndex = 0
                        ConfigService.save()
                    }
                ) {
                    Text(
                        redirectText, fontSize = 13.sp, lineHeight = 1.sp,
                        modifier = Modifier.height(20.dp)
                    )
                }
                Tab(
                    modifier = Modifier.width(45.dp).height(25.dp).enableFocusable(),
                    selected = selectedTabIndex == 1,
                    onClick = {
                        FlyShadowCore.setUdpProxyType(3)
                        config.udpProxyType = 2
                        selectedTabIndex = 1
                        ConfigService.save()
                    }
                ) {
                    Text(
                        ruleText, fontSize = 13.sp, lineHeight = 1.sp,
                        modifier = Modifier.height(20.dp)
                    )
                }
                Tab(
                    modifier = Modifier.width(50.dp).height(25.dp).enableFocusable(),
                    selected = selectedTabIndex == 2,
                    onClick = {
                        FlyShadowCore.setUdpProxyType(2)
                        config.udpProxyType = 1
                        selectedTabIndex = 2
                        ConfigService.save()
                    }
                ) {
                    Text(
                        globalText, fontSize = 13.sp, lineHeight = 1.sp,
                        modifier = Modifier.height(20.dp)
                    )
                }
            }
        }
    }
}


@Composable
fun NativeUdpEnableView(viewModel: ViewModel) {
    if (config.getSelectNodeInfo()?.nativeUdp?.toInt() != 1) {
        return
    }
    Divider(startIndent = 40.dp)
    viewModel.language
    var launchOnStartUp by remember { mutableStateOf(config.nativeUdp) }
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 10.dp).enableFocusable(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.udp),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.nativeUdp),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
            )
            Switch(
                modifier = Modifier.enableFocusable(),
                checked = launchOnStartUp,
                onCheckedChange = {
                    launchOnStartUp = it
                    config.nativeUdp = it
                    FlyShadowCore.setNativeUdp(it)
                    ConfigService.save()
                },
                colors = appSwitchColors()
            )
        }
    }
}

var DirectConnPrioritySaveJob: Job? = null

@OptIn(DelicateCoroutinesApi::class)
@Composable
fun DirectConnPriorityEnableView(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    var directConnPriority by remember { mutableStateOf(config.directConnPriority) }
    var directConnPriorityTimeout by remember { mutableStateOf(config.directConnPriorityTimeout.toString()) }
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 10.dp).enableFocusable(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.redirect_conn),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.directConnPriority),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
            )
            Switch(
                modifier = Modifier.enableFocusable(),
                checked = directConnPriority,
                onCheckedChange = {
                    directConnPriority = it
                    config.directConnPriority = it
                    FlyShadowCore.setDirectConnPriority(config.directConnPriority, config.directConnPriorityTimeout)
                    ConfigService.save()
                },
                colors = appSwitchColors()
            )
        }
    }
    // 直连优先超时时间
    if (directConnPriority) {
        Divider(startIndent = 40.dp)
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxWidth().height(35.dp)
                .clip(RoundedCornerShape(10.dp))
        ) {
            Row(
                Modifier.padding(start = 39.dp, end = 10.dp).enableFocusable(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(Res.drawable.redirect_conn),
                    contentDescription = "", modifier = Modifier.size(19.dp)
                )
                Text(
                    stringResource(Res.string.directConnectTimeout),
                    fontSize = 13.sp,
                    lineHeight = 1.sp,
                    modifier = Modifier.padding(start = 5.dp, end = 5.dp)
                )
                CustomTextField(
                    keyboardOptions = KeyboardOptions.Default.copy(keyboardType = KeyboardType.Number),
                    value = directConnPriorityTimeout,
                    onValueChange = {
                        directConnPriorityTimeout = it
                        if (it.isNotEmpty() && (it.all { char -> char.isDigit() } && it.toLong() >= 0)) {
                            config.directConnPriorityTimeout = directConnPriorityTimeout.toLong()
                            FlyShadowCore.setDirectConnPriority(
                                config.directConnPriority,
                                config.directConnPriorityTimeout
                            )
                            DirectConnPrioritySaveJob?.cancel()
                            DirectConnPrioritySaveJob = GlobalScope.launch(Dispatchers.IO) {
                                delay(3000)
                                ConfigService.save()
                            }
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .enableFocusable()
                        .padding(horizontal = 15.dp, vertical = 5.dp)
                        .background(MaterialTheme.colors.surface)
                        .height(30.dp),
                    singleLine = true,
                    textStyle = TextStyle(fontSize = 12.sp),
                    contentPadding = PaddingValues(5.dp)
                )

            }
        }
    }
}


@OptIn(DelicateCoroutinesApi::class)
@Composable
fun StartupEnableView(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    var startupEnable by remember { mutableStateOf(StartupUtil.getInstance().getStartupStatus()) }
    var startUpAsAdministrator by remember { mutableStateOf(config.startUpAsAdmin) }
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.startup),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.runOnPowerOn),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
            )
            Switch(
                startupEnable,
                onCheckedChange = {
                    GlobalScope.launch(Dispatchers.IO) {
                        if (it) {
                            StartupUtil.getInstance().setStartup(startUpAsAdministrator)
                        } else {
                            StartupUtil.getInstance().removeStartup()
                        }
                        startupEnable = StartupUtil.getInstance().getStartupStatus()
                    }
                },
                colors = appSwitchColors()
            )
        }
    }
    if (SystemUtil.isWindows() && startupEnable) {
        Divider(startIndent = 40.dp)
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth().height(35.dp)
                .padding(start = 29.dp)
                .clip(RoundedCornerShape(10.dp))
        ) {
            Row(
                Modifier.padding(start = 10.dp, end = 10.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(Res.drawable.key),
                    contentDescription = "", modifier = Modifier.size(19.dp)
                )
                Text(
                    stringResource(Res.string.startUpAsAdministrator),
                    fontSize = 13.sp,
                    lineHeight = 1.sp,
                    modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
                )
                Switch(
                    startUpAsAdministrator,
                    onCheckedChange = {
                        GlobalScope.launch(Dispatchers.IO) {
                            startUpAsAdministrator = it
                            config.startUpAsAdmin = it
                            ConfigService.save()
                            StartupUtil.getInstance().removeStartup()
                            StartupUtil.getInstance().setStartup(startUpAsAdministrator)
                        }
                    },
                    colors = appSwitchColors()
                )
            }
        }
    }
}

@OptIn(DelicateCoroutinesApi::class)
@Composable
fun AutoSetSysProxyOnStartUpView(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    var autoSetSysProxyOnStartUp by remember { mutableStateOf(config.autoSetSysProxyOnStartUp) }
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.network_proxy),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.autoSetSysProxyOnStartUp),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
            )
            Switch(
                autoSetSysProxyOnStartUp,
                onCheckedChange = {
                    autoSetSysProxyOnStartUp = it
                    config.autoSetSysProxyOnStartUp = it
                    if (it && viewModel.proxyStatus == ProxyStatus.START) {
                        SystemProxyUtil.enableProxy("127.0.0.1", config.proxyPort)
                    }
                    GlobalScope.launch(Dispatchers.IO) {
                        ConfigService.save()
                    }
                },
                colors = appSwitchColors()
            )
        }
    }
}


@Composable
fun LaunchOnStartupView(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    var launchOnStartUp by remember { mutableStateOf(config.launchOnStartUp) }
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth().height(35.dp)
            .clip(RoundedCornerShape(10.dp))
    ) {
        Row(
            Modifier.padding(start = 10.dp, end = 10.dp).enableFocusable(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.launch),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Text(
                stringResource(Res.string.connectServerOnRun),
                fontSize = 13.sp,
                lineHeight = 1.sp,
                modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)
            )
            Switch(
                modifier = Modifier.enableFocusable(),
                checked = launchOnStartUp,
                onCheckedChange = {
                    launchOnStartUp = it
                    config.launchOnStartUp = it
                    ConfigService.save()
                },
                colors = appSwitchColors()
            )
        }
    }
}

@Composable
expect fun MinimizeAfterStartupView(viewModel: ViewModel)

@Composable
fun AppVersion(viewModel: ViewModel) {
    Divider(startIndent = 40.dp)
    viewModel.language
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.fillMaxWidth()
            .height(35.dp)
            .clip(RoundedCornerShape(10.dp))
            .enableFocusable()
            .clickable {
                ViewFunction.pushNavigationStackPage(VersionInfoPage())
            }
    ) {
        Row(
            Modifier.weight(1f).padding(start = 10.dp, end = 20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(Res.drawable.version),
                contentDescription = "", modifier = Modifier.size(19.dp)
            )
            Column(modifier = Modifier.padding(start = 5.dp).weight(weight = 1f)) {
                Text(
                    stringResource(Res.string.versionDetail),
                    fontSize = 13.sp,
                    lineHeight = 1.sp
                )
            }
            Row(Modifier.padding(start = 5.dp)) {
                Text("v${GradleProperties.getVersion()}", fontSize = 13.sp, lineHeight = 13.sp)
                viewModel.version?.let {
                    Text("($it)", fontSize = 13.sp, lineHeight = 13.sp, color = MaterialTheme.colors.error)
                }
            }
        }
        if (viewModel.downloadProcess > 0 && viewModel.downloadProcess < 100) {
            LinearProgressIndicator(progress = viewModel.downloadProcess, modifier = Modifier.height(2.dp))
        }
    }
}
