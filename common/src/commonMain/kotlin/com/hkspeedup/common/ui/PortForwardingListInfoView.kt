package com.hkspeedup.common.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hkspeedup.common.entension.enableFocusable
import com.hkspeedup.common.service.PortForwardingService
import com.hkspeedup.common.service.config
import com.hkspeedup.common.ui.component.BackNavBar
import com.hkspeedup.common.ui.component.MyVerticalScrollbar
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.viewmodel.*
import com.hkspeedup.common.vo.ClientVo
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.*
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource


@OptIn(DelicateCoroutinesApi::class)
@Composable
fun PortForwardingListInfoView(viewModel: ViewModel) {
    val clientList = remember { mutableStateListOf<ClientVo>() }
    val portForwardingList = remember { config.portForwardingList.toMutableStateList() }
    var uuid by remember { mutableStateOf(config.uuid) }
    var remark by remember { mutableStateOf("") }

    DisposableEffect(Unit) {
        val job = GlobalScope.launch(Dispatchers.IO) {
            ViewFunction.setWindowLoading(true)
            kotlin.runCatching {
                PortForwardingService.getClientList(config.subscribePassword, viewModel)?.let { it ->
                    clientList.clear()
                    clientList.addAll(it)
                    remark = clientList.firstOrNull { it.uuid == uuid }?.remark ?: ""
                }
                PortForwardingService.getPortForwardingList(config.subscribePassword, viewModel)
                portForwardingList.clear()
                portForwardingList.addAll(config.portForwardingList)
            }.onFailure {
                GlobalLog.error(it, "获取端口转发信息失败")
                if (it.message?.contains("401") == true) {
                    ViewFunction.showAlertDialog(AlertDialogMessage(StringContentVo(Res.string.loginFailure, null)))
                }
            }

            ViewFunction.setWindowLoading(false)
        }
        // 使用 DisposableEffect 来清理资源或取消工作
        onDispose {
            ViewFunction.setWindowLoading(false)
            job.cancel() // 在界面消失时取消协程
        }
    }

    Box(
        Modifier.padding(10.dp)
            .heightIn(min = 100.dp)
            .background(MaterialTheme.colors.surface, shape = RoundedCornerShape(10.dp))
    ) {
        val state = rememberLazyListState()

        // 连接列表标题
        Column {
            BackNavBar(stringResource(Res.string.portForwarding)) {
                TextButton(onClick = {
                    PortForwardingDetailView.portForwardingVo = null
                    ViewFunction.pushNavigationStackPage(PortForwardingDetailPage())
                }, modifier = Modifier.enableFocusable()) {
                    Text(stringResource(Res.string.create))
                }
            }

            Divider(Modifier.padding(start = 10.dp))

            // 客户端ID
            Row(modifier = Modifier.padding(horizontal = 5.dp), verticalAlignment = Alignment.CenterVertically) {
                Text(
                    modifier = Modifier.weight(1f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    text = "${stringResource(Res.string.client)} ID: $uuid",
                    fontSize = 13.sp,
                    lineHeight = 13.sp,
                    color = MaterialTheme.colors.onSurface
                )
                TextButton(onClick = {
                    GlobalScope.launch(Dispatchers.IO) {
                        ViewFunction.setWindowLoading(true)
                        kotlin.runCatching {
                            PortForwardingService.resetUuid(config.subscribePassword, viewModel)
                            uuid = config.uuid
                        }.onFailure {
                            GlobalLog.error(it, "获取端口转发信息失败")
                            if (it.message?.contains("401") == true) {
                                ViewFunction.showAlertDialog(
                                    AlertDialogMessage(
                                        StringContentVo(
                                            Res.string.loginFailure,
                                            null
                                        )
                                    )
                                )
                            }
                        }

                        ViewFunction.setWindowLoading(false)
                    }
                }, modifier = Modifier.enableFocusable().height(20.dp), contentPadding = PaddingValues(1.dp)) {
                    Text(stringResource(Res.string.reset), fontSize = 13.sp, lineHeight = 13.sp)
                }
            }
            // 客户端备注
            Row(modifier = Modifier.padding(horizontal = 5.dp), verticalAlignment = Alignment.CenterVertically) {
                Text(
                    modifier = Modifier.weight(1f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    text = "${stringResource(Res.string.remark)} : $remark",
                    fontSize = 13.sp,
                    lineHeight = 13.sp,
                    color = MaterialTheme.colors.onSurface
                )
                TextButton(onClick = {
                    GlobalScope.launch(Dispatchers.IO) {
                        PortForwardingRemarkView.remark = remark
                        ViewFunction.pushNavigationStackPage(PortForwardingRemarkPage())
                    }
                }, modifier = Modifier.enableFocusable().height(20.dp), contentPadding = PaddingValues(1.dp)) {
                    Text(stringResource(Res.string.edit), fontSize = 13.sp, lineHeight = 13.sp)
                }
            }
            Divider()

            if (portForwardingList.isEmpty()) {
                return
            }

            Box(
                modifier = Modifier.fillMaxWidth()
            ) {
                // 连接列表
                LazyColumn(state = state, modifier = Modifier.padding(start = 5.dp, end = 5.dp)) {
                    items(portForwardingList.size, key = { portForwardingList[it].id!! }) { index ->
                        val portForwardingVo = portForwardingList[index]

                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = portForwardingVo.uuid.takeIf { it == uuid }
                                ?.let { Modifier.background(MaterialTheme.colors.primary.copy(alpha = 0.15f)) }
                                ?: Modifier
                        ) {
                            // 内容
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                modifier = Modifier.weight(1f).enableFocusable()
                                    .clickable {
                                        // 编辑点击事件
                                        PortForwardingDetailView.portForwardingVo = portForwardingVo.copy()
                                        ViewFunction.pushNavigationStackPage(PortForwardingDetailPage())
                                    },
                            ) {
                                Column(
                                    Modifier.weight(1f).padding(10.dp),
                                    verticalArrangement = Arrangement.Center
                                ) {

                                    Text(
                                        "${stringResource(Res.string.client)} ID: ${portForwardingVo.uuid}",
                                        fontSize = 13.sp,
                                        lineHeight = 13.sp,
                                        color = MaterialTheme.colors.onSurface,
                                        style = TextStyle(textDecoration = if (portForwardingVo.enable) null else TextDecoration.LineThrough)
                                    )


                                    Text(
                                        "${stringResource(Res.string.listenPort)}: ${portForwardingVo.listenPort}",
                                        fontSize = 13.sp,
                                        lineHeight = 13.sp,
                                        color = MaterialTheme.colors.onSurface,
                                        style = TextStyle(textDecoration = if (portForwardingVo.enable) null else TextDecoration.LineThrough)
                                    )


                                    Text(
                                        "${stringResource(Res.string.targetAddress)}: ${portForwardingVo.targetAddr}",
                                        fontSize = 13.sp,
                                        lineHeight = 13.sp,
                                        color = MaterialTheme.colors.onSurface,
                                        style = TextStyle(textDecoration = if (portForwardingVo.enable) null else TextDecoration.LineThrough)
                                    )

                                    val remarkStr = clientList.firstOrNull { it.uuid == portForwardingVo.targetUuid }
                                        ?.remark?.takeIf { it.isNotBlank() }?.let { "($it)" } ?: ""
                                    Text(
                                        "${stringResource(Res.string.targetClient)} ID: ${portForwardingVo.targetUuid} $remarkStr",
                                        fontSize = 13.sp,
                                        lineHeight = 13.sp,
                                        color = MaterialTheme.colors.onSurface,
                                        style = TextStyle(textDecoration = if (portForwardingVo.enable) null else TextDecoration.LineThrough)
                                    )

                                    portForwardingVo.errorMessage?.takeIf { it.isNotBlank() }?.let {
                                        Text(
                                            "${stringResource(Res.string.error)} : $it",
                                            fontSize = 13.sp,
                                            lineHeight = 13.sp,
                                            color = Color.Black
                                        )
                                    }
                                }

                                Image(
                                    painter = painterResource(Res.drawable.right),
                                    contentDescription = "",
                                    modifier = Modifier.size(15.dp),
                                )

                            }

                        }
                        Divider(Modifier.padding(start = 10.dp))
                    }
                }
                MyVerticalScrollbar(
                    modifier = Modifier.align(Alignment.CenterEnd), state = state
                )
            }
        }

    }

}