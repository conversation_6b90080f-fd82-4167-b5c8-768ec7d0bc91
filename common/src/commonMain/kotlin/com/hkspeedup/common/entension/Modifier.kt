package com.hkspeedup.common.entension

import androidx.compose.foundation.Indication
import androidx.compose.foundation.focusable
import androidx.compose.foundation.indication
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.material.MaterialTheme
import androidx.compose.material.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier

@Composable
fun Modifier.enableFocusable(): Modifier {
    val interactionSource: MutableInteractionSource = remember { MutableInteractionSource() }
    val rippleIndication: Indication = ripple(color = MaterialTheme.colors.primary)
    return this.focusable(interactionSource = interactionSource)
        .indication(interactionSource = interactionSource, indication = rippleIndication)
}