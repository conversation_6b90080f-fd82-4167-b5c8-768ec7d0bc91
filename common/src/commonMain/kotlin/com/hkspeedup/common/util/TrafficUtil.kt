package com.hkspeedup.common.util

import java.math.BigDecimal
import java.math.RoundingMode

class TrafficUtil {
    companion object {

        fun getTrafficString(traffic: Long, trafficUnitEnum: TrafficUnitEnum?): String {
            var traffic = traffic
            var unit = ""
            if (traffic < 0) {
                traffic = -traffic
                unit = "-"
            }
            var index = 0
            var trafficFloat = traffic.toFloat()
            while (trafficFloat > 1024) {
                if (TrafficUnitEnum.values()[index] == trafficUnitEnum) {
                    break
                }
                trafficFloat /= 1024f
                index++
            }
            return unit + BigDecimal(trafficFloat.toDouble()).setScale(
                2,
                RoundingMode.HALF_DOWN
            ) + TrafficUnitEnum.values()[index].name
        }

        fun getTrafficLong(trafficStr: String): Long? {
            var trafficUnitEnum: TrafficUnitEnum? = null
            for (value in TrafficUnitEnum.values()) {
                val i: Int = trafficStr.lastIndexOf(value.name)
                if (i != -1) {
                    trafficUnitEnum = value
                }
            }
            if (trafficUnitEnum == null) {
                return null
            }
            var trafficUnit: Long = 1
            val ordinal: Int = trafficUnitEnum.ordinal
            for (i in 0 until ordinal) {
                trafficUnit = trafficUnit * 1024
            }
            return try {
                val traffic = trafficStr.substring(0, trafficStr.indexOf(trafficUnitEnum.name)).toFloat()
                (traffic * trafficUnit).toLong()
            } catch (e: NumberFormatException) {
                throw RuntimeException("流量格式错误: $trafficStr")
            }
        }
    }
}