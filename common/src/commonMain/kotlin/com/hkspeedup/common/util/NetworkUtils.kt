package com.hkspeedup.common.util

import java.net.NetworkInterface


enum class IpType {
    IPV4,
    IPV6
}

object NetworkUtils {

    fun getLocalIPAddresses(): <PERSON>tableMap<IpType, MutableList<String>> {
        val ipAddresses: Mu<PERSON>Map<IpType, MutableList<String>> = mutableMapOf()

        try {
            val interfaces = NetworkInterface.getNetworkInterfaces()

            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()

                // 过滤掉没有启用的接口
                if (!networkInterface.isUp || networkInterface.isLoopback) {
                    continue
                }

                val addresses = networkInterface.inetAddresses
                while (addresses.hasMoreElements()) {
                    val address = addresses.nextElement()
                    if (address.hostAddress.contains(".")) {
                        ipAddresses.computeIfAbsent(IpType.IPV4) { mutableListOf() }.add(address.hostAddress)
                    } else if (address.hostAddress.contains(":")) {
                        ipAddresses.computeIfAbsent(IpType.IPV6) { mutableListOf() }
                            .add(address.hostAddress.substringBeforeLast("%"))
                    }
                }
            }
        } catch (e: Exception) {
            GlobalLog.error(e, "获取本地网卡信息错误")
        }
        return ipAddresses
    }
}