package com.hkspeedup.common.util

import cn.hutool.system.SystemUtil
import com.hkspeedup.common.service.config

object CommandUtil {

    fun getProxySettingsCommand(): String {
        return if (SystemUtil.getOsInfo().isWindows) {
            "set http_proxy=http://127.0.0.1:${config.proxyPort}\nset https_proxy=http://127.0.0.1:${config.proxyPort}\n"
        } else if (SystemUtil.getOsInfo().isMac) {
            "export http_proxy=http://127.0.0.1:${config.proxyPort}\nexport https_proxy=http://127.0.0.1:${config.proxyPort}\n"
        } else {
            "Unknown System Version"
        }
    }
}