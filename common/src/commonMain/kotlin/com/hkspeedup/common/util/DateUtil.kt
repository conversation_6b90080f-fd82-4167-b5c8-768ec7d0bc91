package com.hkspeedup.common.util

import cn.hutool.core.date.BetweenFormatter
import cn.hutool.core.date.DateUnit
import cn.hutool.core.date.DateUtil
import java.io.Serializable
import java.text.SimpleDateFormat
import java.util.*


object DateUtil {
    // 使用 SimpleDateFormat 进行格式化
    fun formatDateTime(date: Date?, pattern: String?): String {
        val sdf = SimpleDateFormat(pattern, Locale.getDefault())
        return sdf.format(date)
    }

    // 使用 SimpleDateFormat 进行解析
    fun parseDateTime(dateString: String?, pattern: String?): Date {
        val sdf = SimpleDateFormat(pattern, Locale.getDefault())
        return sdf.parse(dateString)
    }

    fun formatBetween(beginDate: Date, endDate: Date, level: BetweenFormatter.Level): String {
        return cn.hutool.core.date.DateUtil.formatBetween(DateBetween(beginDate, endDate).between(DateUnit.MS), level)
    }
}

class DateBetween @JvmOverloads constructor(begin: Date, end: Date, isAbs: Boolean = true) :
    Serializable {
    /**
     * 开始日期
     */
    private var begin: Date? = null

    /**
     * 结束日期
     */
    private var end: Date? = null

    /**
     * 构造<br></br>
     * 在前的日期做为起始时间，在后的做为结束时间
     *
     * @param begin 起始时间
     * @param end   结束时间
     * @param isAbs 日期间隔是否只保留绝对值正数
     * @since 3.1.1
     */
    /**
     * 构造<br></br>
     * 在前的日期做为起始时间，在后的做为结束时间，间隔只保留绝对值正数
     *
     * @param begin 起始时间
     * @param end   结束时间
     */
    init {
        if (isAbs && begin.after(end)) {
            // 间隔只为正数的情况下，如果开始日期晚于结束日期，置换之
            this.begin = end
            this.end = begin
        } else {
            this.begin = begin
            this.end = end
        }
    }

    /**
     * 判断两个日期相差的时长<br></br>
     * 返回 给定单位的时长差
     *
     * @param unit 相差的单位：相差 天[DateUnit.DAY]、小时[DateUnit.HOUR] 等
     * @return 时长差
     */
    fun between(unit: DateUnit): Long {
        val diff = end!!.time - begin!!.time
        return diff / unit.millis
    }

    /**
     * 计算两个日期相差月数<br></br>
     * 在非重置情况下，如果起始日期的天大于结束日期的天，月数要少算1（不足1个月）
     *
     * @param isReset 是否重置时间为起始时间（重置天时分秒）
     * @return 相差月数
     * @since 3.0.8
     */
    fun betweenMonth(isReset: Boolean): Long {
        val beginCal = DateUtil.calendar(begin)
        val endCal = DateUtil.calendar(end)

        val betweenYear = endCal[Calendar.YEAR] - beginCal[Calendar.YEAR]
        val betweenMonthOfYear = endCal[Calendar.MONTH] - beginCal[Calendar.MONTH]

        val result = betweenYear * 12 + betweenMonthOfYear
        if (false == isReset) {
            endCal[Calendar.YEAR] = beginCal[Calendar.YEAR]
            endCal[Calendar.MONTH] = beginCal[Calendar.MONTH]
            val between = endCal.timeInMillis - beginCal.timeInMillis
            if (between < 0) {
                return (result - 1).toLong()
            }
        }
        return result.toLong()
    }

    /**
     * 计算两个日期相差年数<br></br>
     * 在非重置情况下，如果起始日期的月大于结束日期的月，年数要少算1（不足1年）
     *
     * @param isReset 是否重置时间为起始时间（重置月天时分秒）
     * @return 相差年数
     * @since 3.0.8
     */
    fun betweenYear(isReset: Boolean): Long {
        val beginCal = DateUtil.calendar(begin)
        val endCal = DateUtil.calendar(end)

        val result = endCal[Calendar.YEAR] - beginCal[Calendar.YEAR]
        if (false == isReset) {
            // 考虑闰年的2月情况
            if (Calendar.FEBRUARY == beginCal[Calendar.MONTH] && Calendar.FEBRUARY == endCal[Calendar.MONTH]) {
                if (beginCal[Calendar.DAY_OF_MONTH] == beginCal.getActualMaximum(Calendar.DAY_OF_MONTH)
                    && endCal[Calendar.DAY_OF_MONTH] == endCal.getActualMaximum(Calendar.DAY_OF_MONTH)
                ) {
                    // 两个日期都位于2月的最后一天，此时月数按照相等对待，此时都设置为1号
                    beginCal[Calendar.DAY_OF_MONTH] = 1
                    endCal[Calendar.DAY_OF_MONTH] = 1
                }
            }

            endCal[Calendar.YEAR] = beginCal[Calendar.YEAR]
            val between = endCal.timeInMillis - beginCal.timeInMillis
            if (between < 0) {
                return (result - 1).toLong()
            }
        }
        return result.toLong()
    }

    /**
     * 格式化输出时间差
     *
     * @param unit  日期单位
     * @param level 级别
     * @return 字符串
     * @since 5.7.17
     */
    fun toString(unit: DateUnit, level: BetweenFormatter.Level?): String {
        return DateUtil.formatBetween(between(unit), level)
    }

    /**
     * 格式化输出时间差
     *
     * @param level 级别
     * @return 字符串
     */
    fun toString(level: BetweenFormatter.Level?): String {
        return toString(DateUnit.MS, level)
    }

    override fun toString(): String {
        return toString(BetweenFormatter.Level.MILLISECOND)
    }
}