package com.hkspeedup.common.util

import java.io.File
import java.io.IOException

expect fun getConfigFilePath():String

expect fun openProgramInstallFile(filePath: String)

expect fun openFileDir(filePath: String);

expect fun importJsonSubscribeFile()

object FileUtil {
    /**
     * 创建目录（如果不存在）
     *
     * @param directoryPath 目录路径
     * @return true 如果成功创建或目录已经存在；false 如果创建失败
     */
    fun mkdir(directoryPath: String?): Boolean {
        val directory = File(directoryPath)
        if (!directory.exists()) {
            return directory.mkdirs()
        }
        return true
    }

    /**
     * 删除文件或目录（包括子目录和文件）
     *
     * @param fileOrDirectoryPath 文件或目录的路径
     * @return true 如果成功删除；false 如果删除失败
     */
    fun del(fileOrDirectoryPath: String?): Boolean {
        val fileOrDirectory = File(fileOrDirectoryPath)
        if (fileOrDirectory.exists()) {
            return deleteFileOrDirectory(fileOrDirectory)
        }
        return true
    }

    /**
     * 创建文件（如果不存在），如果文件已存在则更新修改时间
     *
     * @param filePath 文件路径
     * @return true 如果成功创建或文件已存在；false 如果创建失败
     */
    fun touch(filePath: String?): Boolean {
        val file = File(filePath)
        if (!file.exists()) {
            try {
                return file.createNewFile()
            } catch (e: IOException) {
                e.printStackTrace()
                return false
            }
        }
        return true
    }

    /**
     * 递归删除文件或目录
     */
    private fun deleteFileOrDirectory(fileOrDirectory: File): Boolean {
        if (fileOrDirectory.isDirectory) {
            val children = fileOrDirectory.listFiles()
            if (children != null) {
                for (child in children) {
                    val success = deleteFileOrDirectory(child)
                    if (!success) {
                        return false
                    }
                }
            }
        }
        return fileOrDirectory.delete()
    }

    /**
     * 计算文件夹内容大小
     */
    fun getFolderSize(folder: File): Long {
        if (!folder.isDirectory) {
            GlobalLog.error("The provided file is not a directory")
            return 0
        }

        return folder.walk()
            .filter { it.isFile }
            .map { it.length() }
            .sum()
    }

    /**
     * 清空文件夹内容
     */
    fun deleteFolderContents(folder: File) {
        if (!folder.isDirectory) {
            throw IllegalArgumentException("The provided file is not a directory")
        }

        folder.listFiles()?.forEach { file ->
            if (file.isDirectory) {
                deleteFolderContents(file) // 递归删除子目录
            }
            file.delete() // 删除文件或空目录
        }
    }
}
