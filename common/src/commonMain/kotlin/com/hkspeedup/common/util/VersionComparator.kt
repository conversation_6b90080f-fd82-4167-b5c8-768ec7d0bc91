class VersionComparator private constructor() : Comparator<String> {

    override fun compare(version1: String, version2: String): Int {
        // Remove prefix 'v' if present
        val cleanedVersion1 = removePrefix(version1)
        val cleanedVersion2 = removePrefix(version2)

        val parts1 = cleanedVersion1.split('.')
        val parts2 = cleanedVersion2.split('.')

        val length = maxOf(parts1.size, parts2.size)

        for (i in 0 until length) {
            val part1 = parts1.getOrNull(i)?.toIntOrNull() ?: 0
            val part2 = parts2.getOrNull(i)?.toIntOrNull() ?: 0

            if (part1 != part2) {
                return part1 - part2
            }
        }

        return 0
    }

    private fun removePrefix(version: String): String {
        return if (version.startsWith("v")) version.substring(1) else version
    }

    companion object {
        val INSTANCE: VersionComparator = VersionComparator()
    }
}
