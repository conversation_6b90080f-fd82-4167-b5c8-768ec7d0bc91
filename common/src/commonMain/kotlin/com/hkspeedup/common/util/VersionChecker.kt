package com.hkspeedup.common.util

import VersionComparator
import cn.hutool.core.io.IoUtil
import cn.hutool.core.util.StrUtil
import com.hkspeedup.common.service.FlyShadowApiService
import com.hkspeedup.common.service.HTTPS_API_FLYSHADOW_URL
import com.hkspeedup.common.service.config
import com.hkspeedup.common.viewmodel.AlertDialogMessage
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewFunction
import com.hkspeedup.common.viewmodel.ViewModel
import com.hkspeedup.common.vo.AppVersion
import com.hkspeedup.common.vo.StringContentVo
import flyshadow.common.generated.resources.Res
import flyshadow.common.generated.resources.deleteFileError
import flyshadow.common.generated.resources.downloadError
import flyshadow.common.generated.resources.networkError
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.math.BigDecimal
import java.math.RoundingMode
import java.net.HttpURLConnection
import java.net.InetSocketAddress
import java.net.Proxy
import java.net.URL
import java.util.*
import java.util.zip.ZipInputStream


object VersionChecker {

    private var downloadJob: Job? = null  // 存储下载任务
    private var time: Long? = null  // 用于标记下载开始时间，避免多次下载

    private var appVersion: AppVersion? = null  // 存储应用版本信息

    /**
     * 获取当前应用版本
     */
    private fun getCurrentVersion(): String {
        // 在这里编写获取当前版本的逻辑，这里简单地返回一个示例版本号
        return "v${GradleProperties.getVersion()}"
    }

    /**
     * 获取版本信息
     */
    private suspend fun getVersionInfo(viewModel: ViewModel) {
        appVersion = null

        // 创建FlyShadowApi服务，判断是否启用代理
        val flyShadowApi = FlyShadowApiService.createApi(viewModel.proxyStatus == ProxyStatus.START)

        runCatching {
            // 获取应用版本列表
            val appVersionList = flyShadowApi.getAppVersionList()
            val platformId = PlatformUtil.getPlatformId()
            // 查找对应平台的版本信息
            appVersion = appVersionList.data?.find { it.platform == platformId }
        }.onFailure {
            GlobalLog.error(it, "获取应用版本信息失败")
        }
    }

    /**
     * 比较当前版本和最新版本，判断是否需要更新
     */
    private fun compareVersions(currentVersion: String, latestVersion: String): Boolean {
        return VersionComparator.INSTANCE.compare(latestVersion, currentVersion) > 0
    }

    /**
     * 检查是否有更新
     */
    suspend fun checkForUpdate(viewModel: ViewModel) {
        val currentVersion = getCurrentVersion()
        try {
            // 获取版本信息
            getVersionInfo(viewModel)
            val latestVersion = appVersion?.version
            if (latestVersion == null) {
                GlobalLog.info("最新版本信息为空")
                return
            }
            // 比较版本号，判断是否有新版本
            if (compareVersions(currentVersion, latestVersion)) {
                GlobalLog.info("有新版本可用：$latestVersion")
                viewModel.version = latestVersion  // 更新最新版本
                viewModel.versionDesc = appVersion?.desc // 版本描述
            } else {
                GlobalLog.info("当前已经是最新版本")
            }
        } catch (e: Exception) {
            GlobalLog.error("检查更新失败: ${e.message}")
        }
    }

    /**
     * 取消下载任务
     */
    fun cancelDownload(viewModel: ViewModel) {
        time = Date().time
        downloadJob?.cancel()  // 取消下载任务
        viewModel.downloadProcess = 0f  // 重置下载进度
    }

    /**
     * 下载安装包文件
     */
    @OptIn(DelicateCoroutinesApi::class)
    fun downloadPackageFile(viewModel: ViewModel) {
        time = Date().time
        downloadJob?.cancel()  // 取消之前的下载任务
        downloadJob = GlobalScope.launch(Dispatchers.IO) {
            val innerTime = time
            var progressJob: Job? = null  // 用于更新下载进度
            if (StrUtil.isBlank(viewModel.version)) {
                return@launch
            }
            val id = PlatformUtil.getPlatformId()
            if (id <= 0 || appVersion == null) {
                return@launch
            }

            val fileId = appVersion?.fileId ?: return@launch

            val url = "${HTTPS_API_FLYSHADOW_URL}/client/file/download/${fileId}";

            var connection: HttpURLConnection? = null
            var inputStream: ZipInputStream? = null
            var outputStream: FileOutputStream? = null

            GlobalLog.info("开始下载: $url")

            try {
                viewModel.downloadProcess = 0.00001f
                if (viewModel.proxyStatus == ProxyStatus.START) {
                    // 使用代理下载
                    val proxy = Proxy(Proxy.Type.HTTP, InetSocketAddress("127.0.0.1", config.proxyPort))
                    connection = URL(url).openConnection(proxy) as HttpURLConnection
                } else {
                    // 不使用代理直接下载
                    connection = URL(url).openConnection() as HttpURLConnection
                }
                connection.requestMethod = "GET"
                connection.connectTimeout = 90 * 1000  // 设置连接超时时间
                connection.readTimeout = 90 * 1000  // 设置读取超时时间
                connection.connect()

                // 如果连接失败，抛出异常
                if (connection.responseCode != HttpURLConnection.HTTP_OK) {
                    throw IOException("HTTP 错误码: ${connection.responseCode}")
                }
                inputStream = ZipInputStream(connection.inputStream)
                val fileName = inputStream.nextEntry?.name

                if (fileName == null) {
                    GlobalLog.error("下载文件名为空")
                }

                // 构造文件保存路径
                val filePath =
                    "${getConfigFilePath()}/.FlyShadow/Download/${innerTime}-${fileName}"
                FileUtil.mkdir(filePath)
                FileUtil.del(filePath)
                FileUtil.touch(filePath)

                outputStream = FileOutputStream(File(filePath))

                val totalSize = connection.contentLengthLong  // 文件总大小
                var downloadedSize: Long = 0  // 已下载大小
                val buffer = ByteArray(4096)
                var bytesRead: Int

                // 更新下载进度
                progressJob = GlobalScope.launch(Dispatchers.IO) {
                    while (viewModel.downloadProcess < 100 && innerTime == time) {
                        delay(1000)
                        viewModel.downloadProcess =
                            BigDecimal(downloadedSize.toDouble() / totalSize.toDouble()).setScale(
                                2,
                                RoundingMode.HALF_UP
                            ).toFloat()
                    }
                }
                // 读取并写入数据
                while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                    outputStream.write(buffer, 0, bytesRead)
                    downloadedSize += bytesRead
                    if (innerTime != time) {
                        return@launch
                    }
                }
                // 下载完成后处理
                kotlin.runCatching {
                    if (downloadedSize >= totalSize) {
                        openProgramInstallFile(filePath)  // 安装文件
                    } else {
                        ViewFunction.showAlertDialog(
                            AlertDialogMessage(
                                StringContentVo(
                                    Res.string.downloadError,
                                    null
                                )
                            )
                        )
                    }
                }.onFailure {
                    GlobalLog.error(it, "打开文件时发生错误")
                }
            } catch (e: Exception) {
                // 下载失败，显示错误提示
                ViewFunction.showAlertDialog(
                    AlertDialogMessage(
                        StringContentVo(
                            Res.string.networkError,
                            e.message
                        )
                    )
                )
                GlobalLog.error(e, "下载文件错误")
            } finally {
                progressJob?.cancel()  // 取消进度任务
                IoUtil.close(outputStream)
                IoUtil.close(inputStream)
                connection?.disconnect()  // 断开连接
                viewModel.downloadProcess = 0f  // 重置下载进度
            }
        }
    }

    /**
     * 获取下载文件夹的大小
     */
    fun getDownloadFileSize(): String {
        val downloadFilePath = "${getConfigFilePath()}/.FlyShadow/Download"
        var result = "Error"
        kotlin.runCatching {
            FileUtil.getFolderSize(File(downloadFilePath))  // 获取文件夹大小
        }.onFailure {
            GlobalLog.error("获取文件夹大小失败", it)
        }.onSuccess {
            result = TrafficUtil.getTrafficString(it, null)  // 格式化为合适的单位
        }
        return result
    }

    /**
     * 清理下载文件夹
     */
    fun cleanDownloadFile() {
        val downloadFilePath = "${getConfigFilePath()}/.FlyShadow/Download"
        kotlin.runCatching {
            FileUtil.deleteFolderContents(File(downloadFilePath))  // 删除文件夹内容
        }.onFailure {
            GlobalLog.error(it, "清理下载文件夹失败")
            ViewFunction.showAlertDialog(
                AlertDialogMessage(
                    StringContentVo(
                        Res.string.deleteFileError,
                        it.message
                    )
                )
            )
        }
    }
}
