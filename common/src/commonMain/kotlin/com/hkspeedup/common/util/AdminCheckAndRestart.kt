package com.hkspeedup.common.util

import cn.hutool.core.util.RuntimeUtil
import cn.hutool.core.util.StrUtil
import kotlin.system.exitProcess


object AdminCheckAndRestart {

    fun isAdmin(): Boolean {
        if(SystemUtil.isMac()){
            return true
        }
        val result = RuntimeUtil.execForStr("net session")
        if (StrUtil.containsAny(result, "错误", "拒绝", "管理员", "admin", "Admin", "Error", "error")) {
            GlobalLog.info("Check admin result: $result")
            return false
        }
        return true
    }

    fun restartAsAdmin() {
        val programPath = System.getProperty("jpackage.app-path")

        val vbScript = """
            powershell -Command "Start-Process '$programPath' -Verb RunAs"
        """.trimIndent()

        GlobalLog.info("RestartAsAdmin command: $vbScript")
        val result = RuntimeUtil.execForStr(vbScript)
        GlobalLog.info("RestartAsAdmin result: $result")
        exitProcess(0)
    }

}