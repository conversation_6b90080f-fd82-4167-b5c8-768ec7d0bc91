package com.hkspeedup.common.util

import cn.hutool.core.io.FileUtil
import cn.hutool.core.util.RuntimeUtil
import cn.hutool.system.SystemUtil
import java.nio.charset.Charset


interface StartupUtil {

    fun setStartup(admin: Boolean)

    fun removeStartup()

    fun getStartupStatus(): Boolean

    companion object {
        private var currentStartupUtil: StartupUtil? = null

        fun getInstance(): StartupUtil {
            if (currentStartupUtil != null) {
                return currentStartupUtil!!
            }
            val osInfo = SystemUtil.getOsInfo()
            currentStartupUtil = if (osInfo.isWindows) {
                WindowsStartupUtil()
            } else if (osInfo.isMac) {
                MacStartupUtil()
            } else {
                UnknownSystemStartupUtil()
            }
            return currentStartupUtil!!
        }
    }


}


class WindowsStartupUtil : StartupUtil {
    private val startupLnk =
        "${System.getProperty("user.home")}\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\FlyShadow.lnk"
    private val startupBat =
        "${System.getProperty("user.home")}\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\Startup\\FlyShadowStartup.bat"

    override fun setStartup(admin: Boolean) {
        kotlin.runCatching {
            val runAsAdmin = if (admin) {
                " -Verb RunAs"
            } else {
                ""
            }
            val programPath = System.getProperty("jpackage.app-path")
            GlobalLog.info("Set Startup programDir:$programPath ")
            // 删除快捷方式
            removeStartup()
            FileUtil.writeString(
                "powershell -Command \"Start-Process '$programPath' ${runAsAdmin}\"", startupBat,
                Charset.defaultCharset()
            )
            GlobalLog.info("Create startup bat: $startupBat")
        }.onFailure {
            GlobalLog.error(it, "Create startup error")
        }
    }

    override fun removeStartup() {
        FileUtil.del(startupLnk)
        FileUtil.del(startupBat)
    }

    override fun getStartupStatus(): Boolean {
        return FileUtil.exist(startupBat)
    }

}

class MacStartupUtil : StartupUtil {
    private val startupPlistScript = """
        <?xml version="1.0"encoding="utf-8"?>
        <!DOCTYPE plist PUBLIC"-//Apple//DTD PLIST 1.0//EN"
        "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
        <plist version="1.0">
        <dict>
            <key>Label</key>
            <string>FlyShadow</string>
            <key>ProgramArguments</key>
            <array>
                <string>open</string>
                <string>/Applications/FlyShadow.app</string>
            </array>
            <key>RunAtLoad</key>
            <true/>
        </dict>
        </plist>
    """.trimIndent()

    private val startupFilePath = "${System.getProperty("user.home")}/Library/LaunchAgents/FlyShadow.plist"


    override fun setStartup(admin: Boolean) {
        kotlin.runCatching {
            FileUtil.writeString(startupPlistScript, startupFilePath, Charset.defaultCharset())
            var exec = RuntimeUtil.exec("chmod", "644", startupFilePath)
            if (exec.waitFor() == 0) {
                GlobalLog.info("Startup chmod 664 FlyShadow.plist success: ${RuntimeUtil.getResult(exec)}")
            } else {
                GlobalLog.error("Startup chmod 664 FlyShadow.plist error: ${RuntimeUtil.getResult(exec)}")
                return
            }
        }.onFailure {
            GlobalLog.error(it, "Set startup error")
        }
    }

    override fun removeStartup() {
        kotlin.runCatching {
            val exec = RuntimeUtil.exec("launchctl", "unload", startupFilePath)
            if (exec.waitFor() == 0) {
                GlobalLog.info("Startup launchctl unload FlyShadow.plist success: ${RuntimeUtil.getResult(exec)}")
            } else {
                GlobalLog.error("Startup launchctl unload FlyShadow.plist error: ${RuntimeUtil.getResult(exec)}")
                return
            }
            FileUtil.del(startupFilePath)
        }.onFailure {
            GlobalLog.error(it, "Startup remove error")
        }


    }

    override fun getStartupStatus(): Boolean {
        return FileUtil.exist(startupFilePath)
    }
}

class UnknownSystemStartupUtil : StartupUtil {
    override fun setStartup(admin: Boolean) {

    }

    override fun removeStartup() {
    }

    override fun getStartupStatus(): Boolean {
        return false
    }

}
