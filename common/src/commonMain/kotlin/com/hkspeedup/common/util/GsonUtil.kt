package com.hkspeedup.common.util

import com.google.gson.*
import java.lang.reflect.Type
import java.util.*


object GsonUtil {

    class DateTypeAdapter : JsonSerializer<Date>, JsonDeserializer<Date> {
        override fun serialize(src: Date?, typeOfSrc: Type?, context: JsonSerializationContext?): JsonElement {
            return JsonPrimitive(src?.time)
        }

        override fun deserialize(json: JsonElement?, typeOfT: Type?, context: JsonDeserializationContext?): Date? {
            return json?.asLong?.let { return Date(it) }
        }

    }

    fun createGson(): Gson {
        val gsonBuilder = GsonBuilder()
        gsonBuilder.registerTypeAdapter(Date::class.java, DateTypeAdapter())
        return gsonBuilder.create()
    }
}