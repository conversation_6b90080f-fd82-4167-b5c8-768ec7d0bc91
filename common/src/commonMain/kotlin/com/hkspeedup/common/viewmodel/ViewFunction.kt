package com.hkspeedup.common.viewmodel

import com.hkspeedup.common.vo.StringContentVo

enum class ProxyStatus {
    START, CONNECTING, STOP
}

object ViewFunction {
    var tempAlertDialogMessage: AlertDialogMessage? = null

    var setWindowVisible: (visible: Boolean?) -> Unit = {}
    var setWindowLoading: (visible: Boolean) -> Unit = {}

    fun showAlertDialog(alertDialogMessage: AlertDialogMessage) {
        tempAlertDialogMessage = alertDialogMessage
        showAlertDialogFun(alertDialogMessage) {}
    }

    fun showAlertDialog(alertDialogMessage: AlertDialogMessage, closeCallback: () -> Unit) {
        tempAlertDialogMessage = alertDialogMessage
        showAlertDialogFun(alertDialogMessage, closeCallback)
    }

    var showAlertDialogFun: (tempAlertDialogMessage: AlertDialogMessage, closeCallback: () -> Unit) -> Unit =
        { _: AlertDialogMessage, _: () -> Unit -> }

    var setUploadTraffic: (v: Int) -> Unit = {}
    var setDownloadTraffic: (v: Int) -> Unit = {}

    var setProxyStatus: (proxyStatus: ProxyStatus) -> Unit = {}

    // 页面入栈
    var pushNavigationStackPage: (page: Page) -> Unit = {}

    // 页面出栈
    var backNavigationStackPage: () -> Boolean = { false }
}

data class AlertDialogMessage(val content: StringContentVo, val onClose: (() -> Unit) = {})