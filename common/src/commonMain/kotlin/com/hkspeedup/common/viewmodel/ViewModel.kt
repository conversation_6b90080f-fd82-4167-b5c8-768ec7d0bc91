package com.hkspeedup.common.viewmodel

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.hkspeedup.common.service.config

class ViewModel {
    var proxyStatus by mutableStateOf(ProxyStatus.STOP)
    var tunEnable by mutableStateOf(config.tunModelEnable)
    var tunOpening by mutableStateOf(false)
    var language by mutableStateOf("")
    var version: String? by mutableStateOf(null)
    var versionDesc: String? by mutableStateOf(null)
    var downloadProcess by mutableStateOf(0.0f)
    var darkMode by mutableStateOf(config.darkMode)
}