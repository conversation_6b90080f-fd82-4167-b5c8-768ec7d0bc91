<resources>
    <string name="language">Language</string>
    <string name="english">English</string>
    <string name="chinese">中文</string>
    <string name="server">Server</string>
    <string name="setting">Setting</string>
    <string name="localProxyPort">Local Proxy Port</string>
    <string name="connectList">Connect List</string>
    <string name="customRuleList">Custom Rule List</string>
    <string name="tunMode">TUN Mode</string>
    <string name="cannotBeChangeOnRunning">Cannot be change on running</string>
    <string name="localNetworkInterfaceIP">NIC IPV4</string>
    <string name="localNetworkInterfaceIPv6">IPV6</string>
    <string name="runOnPowerOn">Run on PowerOn</string>
    <string name="autoSetSysProxyOnStartUp">Auto Set System Proxy On Connected</string>
    <string name="connectServerOnRun">Connect Server On Start Up</string>
    <string name="version">Version</string>
    <string name="versionDetail">Version Detail</string>
    <string name="setSubscribe">Set Subscribe</string>
    <string name="subscribePassword">Subscribe Password</string>
    <string name="subscribeContent">Subscribe Content</string>
    <string name="option">Option</string>
    <string name="importSubscribeFile">Import Subscribe File</string>
    <string name="selectFile">Select File</string>
    <string name="importFromFile">Import From File</string>
    <string name="fileImportSuccess">File Import Success</string>
    <string name="fileImportError">File Import Error</string>
    <string name="invalidJsonFile">Invalid JSON File</string>
    <string name="selectingFile">Selecting file...</string>
    <string name="lastUpdate">Last Update</string>
    <string name="delayTest">Delay Test</string>
    <string name="proxyMode">Proxy Mode</string>
    <string name="direct">Direct</string>
    <string name="rule">Rule</string>
    <string name="global">Global</string>
    <string name="proxy">Proxy</string>
    <string name="reject">Reject</string>
    <string name="timeout">Timeout</string>
    <string name="expire">Expire At</string>
    <string name="traffic">Traffic</string>
    <string name="serverList">Server List</string>
    <string name="searchContent">Search Content(Delimit with Spaces)</string>
    <string name="downloadSuccess">Download Success</string>
    <string name="refreshSuccess">Refresh Success</string>
    <string name="updateAndSave">Update And Save</string>
    <string name="hint">Hint</string>
    <string name="ok">OK</string>
    <string name="passwordCannotBeEmpty">Password Cannot Be Empty</string>
    <string name="unknownHostException">Unknown Host Exception</string>
    <string name="error">Error</string>
    <string name="saveConfigFail">Save Config Fail</string>
    <string name="loadCoreFail">Load Core Fail</string>
    <string name="isAlreadyRunning">Program has run</string>
    <string name="openProgram">Open Program</string>
    <string name="exit">Exit</string>
    <string name="restartOnAdministrator">Restart On Administrator</string>
    <string name="startUpAsAdministrator">Start Up As Administrator</string>
    <string name="pleaseSelectServer">Please Select Server</string>
    <string name="connectFail">Connect Fail</string>
    <string name="startTunFail">Failed to start TUN. Please check if you are running as administrator</string>
    <string name="unknownOperatingSystem">Unknown Operating System</string>
    <string name="clickToDownload">ClickToDownload</string>
    <string name="clickToCancel">ClickToCancel</string>
    <string name="installTips">Please exit the application before installing</string>
    <string name="networkError">Network Error</string>
    <string name="downloadError">Download Error</string>
    <string name="appList">App List</string>
    <string name="allowedApplication">Allowed Selection</string>
    <string name="allowedAllApplication">Allowed All Application</string>
    <string name="disallowedApplication">Disallowed Selection</string>
    <string name="nativeUdp">Native UDP</string>
    <string name="minimizeAfterStartUp">Minimize to taskbar after startup</string>
    <string name="deleteFileError">Delete File Error</string>
    <string name="cleanDownloadCache">Clean the download cache</string>
    <string name="openConfigFolder">Open Config Folder</string>
    <string name="start">Start</string>
    <string name="stop">Stop</string>
    <string name="copyProxySettingsCommand">Copy Proxy Settings Command</string>
    <string name="copySuccessful">Copy Successful</string>
    <string name="loginFailure">Login Failure</string>
    <string name="domainNameMatch">Domain Name Match (DOMAIN)</string>
    <string name="domainNameSuffixMatch">Domain Name Suffix Match (DOMAIN-SUFFIX)</string>
    <string name="domainNameKeywordMatch">Domain Name Keyword Match (DOMAIN-KEYWORD)</string>
    <string name="CIDR4Match">CIDR4 Match (IP-CIDR)</string>
    <string name="CIDR6Match">CIDR6 Match (IP-CIDR6)</string>
    <string name="srcCIDR4Match">Source IP Match (SRC-IP-CIDR4)</string>
    <string name="geoIpMatch">GEOIP Database (Country Code) Match (GEOIP)</string>
    <string name="dstPortMatch">Destination Port Match (DST-PORT)</string>
    <string name="srcPortMatch">Source Port Match (SRC-PORT)</string>
    <string name="processNameMatch">Source Process Name Match (PROCESS-NAME)</string>
    <string name="allMatch">All Match (MATCH)</string>
    <string name="clashClassicalMatch">ClashClassical Rule Set Link (Yaml format link)</string>
    <string name="clashDomainMatch">ClashDomain Rule Set Link (Txt format link)</string>
    <string name="create">Create</string>
    <string name="delete">Delete</string>
    <string name="save">Save</string>
    <string name="valueMatching">Value Matching (Domain/IP/Other)</string>
    <string name="matchingRule">Matching Rule</string>
    <string name="enableOrNot">Enable or Not</string>
    <string name="proxyType">Proxy Type</string>
    <string name="onlySupportWin10">Only supports Windows 10 and above</string>
    <string name="directConnPriority">Direct connect priority</string>
    <string name="directConnectTimeout">Direct Connect timeout(ms)</string>
    <string name="ipv6Enable">IPv6 Enable</string>
    <string name="buildInDns">Build-In DNS</string>
    <string name="createTime">CreteTime</string>
    <string name="sourceAddress">SourceAddress</string>
    <string name="targetAddress">TargetAddress</string>
    <string name="protocol">Protocol</string>
    <string name="matchName">MatchName</string>
    <string name="matchRule">MatchRule</string>
    <string name="uploadTraffic">UploadTraffic</string>
    <string name="downloadTraffic">DownloadTraffic</string>
    <string name="uploadTrafficTotal">UploadTrafficTotal</string>
    <string name="downloadTrafficTotal">DownloadTrafficTotal</string>
    <string name="errorPort">Port Error</string>
    <string name="portForwarding">Port Forwarding</string>
    <string name="reset">Reset</string>
    <string name="client">Client</string>
    <string name="listenPort">Listen Port</string>
    <string name="targetClient">Target Client</string>
    <string name="noClientToChoose">No client available to choose</string>
    <string name="remark">Remark</string>
    <string name="edit">Edit</string>
    <string name="trafficMultiplier">Traffic Multiplier</string>
    <string name="darkMode">Dark Mode</string>
    <string name="installPermissionRequired">Install Permission Required</string>
</resources>
