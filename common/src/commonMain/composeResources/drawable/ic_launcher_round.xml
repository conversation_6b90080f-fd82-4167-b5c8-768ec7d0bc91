<vector android:height="143.1309dp" android:viewportHeight="143.48"
    android:viewportWidth="164.4" android:width="164dp"
    xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android">
    <path
        android:pathData="M148.1,43.37c-11.55,-12.15 -28.15,-19.36 -46.2,-18.38 -32.7,1.77 -57.77,29.71 -56,62.41 0.72,13.31 5.78,25.35 13.72,34.84 3.55,1.77 77.87,37.44 88.47,-78.86Z" android:strokeWidth="0">
        <aapt:attr name="android:fillColor">
            <gradient android:endX="148.1" android:endY="76.83"
                android:startX="45.81" android:startY="76.83" android:type="linear">
                <item android:color="#FFF7B52C" android:offset="0"/>
                <item android:color="#FFEA6000" android:offset="1"/>
            </gradient>
        </aapt:attr>
    </path>
    <path android:fillColor="#2ea7e0"
        android:pathData="M55.88,116.91l23.84,-20.87l-19.37,-14.44l-4.47,35.31z" android:strokeWidth="0"/>
    <path
        android:pathData="M45,86.42l-45,-20.15l134.58,-66.27l-31.06,123.37l-58.52,-36.95z" android:strokeWidth="0">
        <aapt:attr name="android:fillColor">
            <gradient android:endX="134.58" android:endY="61.68"
                android:startX="0" android:startY="61.68" android:type="linear">
                <item android:color="#FF94FFFD" android:offset="0.04"/>
                <item android:color="#FF036EB7" android:offset="0.89"/>
            </gradient>
        </aapt:attr>
    </path>
    <path android:fillColor="#fff"
        android:pathData="M17.57,66.91l25.03,10.87l46.68,-31.12l-28.93,34.94l35.99,26.83l23.8,-90.41l-102.57,48.89z" android:strokeWidth="0"/>
    <path android:fillColor="#f39800"
        android:pathData="M60.35,81.6l-4.47,35.31l-13.28,-39.13l46.68,-31.12l-28.93,34.94z" android:strokeWidth="0"/>
    <path
        android:pathData="M59.65,122.26c11.58,13.8 29.28,22.18 48.66,21.13 28.03,-1.52 50.46,-22.27 55.17,-48.77 -13.17,18.65 -50.41,60.18 -103.83,27.64Z" android:strokeWidth="0">
        <aapt:attr name="android:fillColor">
            <gradient android:endX="163.48" android:endY="119.05"
                android:startX="59.65" android:startY="119.05" android:type="linear">
                <item android:color="#FFF7B52C" android:offset="0"/>
                <item android:color="#FFEA6000" android:offset="1"/>
            </gradient>
        </aapt:attr>
    </path>
    <path android:fillColor="#fff"
        android:pathData="M59.49,122.17c0.05,0.03 0.1,0.06 0.15,0.09 0,0 -0.01,-0.02 -0.02,-0.03 -0.09,-0.04 -0.13,-0.07 -0.13,-0.07Z" android:strokeWidth="0"/>
    <path android:fillColor="#fff"
        android:pathData="M148.1,43.37c-10.61,116.3 -84.92,80.64 -88.47,78.86 0,0 0.01,0.02 0.02,0.03 53.42,32.54 90.67,-8.99 103.83,-27.64 0.78,-4.41 1.08,-8.97 0.83,-13.64 -0.79,-14.64 -6.84,-27.76 -16.21,-37.62Z" android:strokeWidth="0"/>
</vector>
