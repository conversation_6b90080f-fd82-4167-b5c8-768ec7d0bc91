#Kotlin
kotlin.code.style=official

#Versions
kotlin.version=2.1.20
agp.version=8.6.0
compose.version=1.8.2

#Gradle
org.gradle.jvmargs=-Xmx2048M -Dkotlin.daemon.jvm.options\="-Xmx2048M"
org.gradle.unsafe.configuration-cache=true

#Compose
org.jetbrains.compose.experimental.uikit.enabled=true
org.jetbrains.compose.experimental.macos.enabled=true
kotlin.native.cacheKind=none
kotlin.native.useEmbeddableCompilerJar=true

#Android
android.useAndroidX=true
android.compileSdk=35
android.targetSdk=33
android.minSdk=21
# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=true
app.versionCode=129
app.version=8.15.0