import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
plugins {
    kotlin("multiplatform")
    kotlin("plugin.compose")
    id("org.jetbrains.compose")

    id("com.android.library")
    id("kotlin-parcelize")

}

kotlin {
    androidTarget()
    jvm("desktop")

    sourceSets {
        all {
            languageSettings {
                optIn("org.jetbrains.compose.resources.ExperimentalResourceApi")
            }
        }
        named("commonMain"){
            dependencies {
                implementation(compose.ui)
                api(compose.runtime)
                api(compose.foundation)
                api(compose.material)
                implementation(compose.components.resources)
                implementation("org.jetbrains.compose.material:material-icons-core:1.7.3")
                implementation("cn.hutool:hutool-core:5.8.21")
                implementation("cn.hutool:hutool-crypto:5.8.21")
                implementation("cn.hutool:hutool-system:5.8.21")
                implementation("com.google.code.gson:gson:2.11.0")
                implementation("io.netty:netty-codec:4.1.96.Final")
                implementation("io.netty:netty-transport:4.1.96.Final")
                implementation("dnsjava:dnsjava:3.5.2")
                implementation("org.apache.commons:commons-lang3:3.12.0")
                implementation("org.modelmapper:modelmapper:2.4.1")
                compileOnly("net.java.dev.jna:jna:5.15.0")
                implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0")
                implementation("com.squareup.okhttp3:okhttp:4.12.0")
                implementation("com.squareup.retrofit2:retrofit:2.11.0")
                implementation("com.squareup.retrofit2:converter-gson:2.11.0")
                implementation("sh.calvin.reorderable:reorderable:2.4.3")
            }
        }
        named("androidMain") {
            dependencies {
                api("androidx.core:core-ktx:1.16.0")
                api("androidx.appcompat:appcompat:1.7.0")
                api("androidx.activity:activity-compose:1.10.1")
                api("androidx.compose.material3:material3:1.3.2")
                api("net.java.dev.jna:jna:5.15.0@aar")
            }
        }
        val desktopMain by getting {
            dependencies {
                implementation(compose.desktop.common)
                implementation("cn.hutool:hutool-http:5.8.21")
                implementation("cn.hutool:hutool-log:5.8.20")
                implementation("org.apache.logging.log4j:log4j-core:2.24.2")
                implementation("net.java.dev.jna:jna:5.15.0")
                implementation("org.jetbrains.kotlinx:kotlinx-coroutines-swing:1.8.0")
            }

            // 复制gradle.properties到资源目录下
            tasks.named<KotlinCompile>("compileKotlinDesktop") {
                dependsOn("copyGradleProperties")
            }
            val copyGradleProperties by tasks.registering(Copy::class) {
                from("..")
                include("gradle.properties")
                into("src/commonMain/resources")
            }

            tasks.named("desktopProcessResources") {
                dependsOn(copyGradleProperties)
            }
        }
    }
}

android {
    compileSdk = (findProperty("android.compileSdk") as String).toInt()
    namespace = "com.hkspeedup.common"

    sourceSets["main"].manifest.srcFile("src/androidMain/AndroidManifest.xml")
    sourceSets["main"].res.srcDirs("src/androidMain/res")
    sourceSets["main"].resources.srcDirs("src/commonMain/resources")
    sourceSets["main"].jniLibs.srcDirs("src/commonMain/resources")

    buildFeatures {
        dataBinding = true
        buildConfig = true
    }
    defaultConfig {
        buildConfigField("String", "versionName", "\"${findProperty("app.version")}\"")
        minSdk = (findProperty("android.minSdk") as String).toInt()
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlin {
        jvmToolchain(11)
    }
}
