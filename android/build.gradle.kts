import com.android.build.gradle.internal.api.ApkVariantOutputImpl

plugins {
    kotlin("multiplatform")
    kotlin("plugin.compose")
    id("com.android.application")
    id("org.jetbrains.compose")
}

kotlin {
    androidTarget()
    sourceSets {
        val androidMain by getting {
            dependencies {
                implementation(project(":common"))
            }
        }
    }
}

android {
    signingConfigs {
        create("release") {
            storeFile = file("../flyshadowvpn.jks")
            storePassword = "qq119520"
            keyAlias = "flyshadowvpn"
            keyPassword = "qq119520"
        }
    }
    packaging {
        resources.excludes.add("META-INF/INDEX.LIST")
        resources.excludes.add("META-INF/io.netty.versions.properties")
    }

    compileSdk = (findProperty("android.compileSdk") as String).toInt()
    namespace = "com.hkspeedup"

    sourceSets["main"].manifest.srcFile("src/androidMain/AndroidManifest.xml")

    buildFeatures {
        dataBinding = true
        buildConfig = true
    }
    defaultConfig {
        buildConfigField("String", "versionName", "\"${findProperty("app.version")}\"")
        minSdk = (findProperty("android.minSdk") as String).toInt()
        applicationId = "com.hkspeedup.FlyShadow"
        minSdk = (findProperty("android.minSdk") as String).toInt()
        targetSdk = (findProperty("android.targetSdk") as String).toInt()
        versionCode = (findProperty("app.versionCode") as String).toInt()
        versionName = findProperty("app.version") as String
        signingConfig = signingConfigs.getByName("release")
    }

    applicationVariants.all {
        this.outputs.all {
            this as ApkVariantOutputImpl
            this.outputFileName = "FlyShadow-android-$versionName.apk"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlin {
        jvmToolchain(11)
    }
}
