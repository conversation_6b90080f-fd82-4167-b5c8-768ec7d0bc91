package com.hkspeedup

import android.content.Intent
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.graphics.drawable.Icon
import android.os.Build
import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import com.hkspeedup.common.MyApplication
import com.hkspeedup.common.R
import com.hkspeedup.common.initMain
import com.hkspeedup.common.service.ConfigService
import com.hkspeedup.common.service.MyVpnService
import com.hkspeedup.common.service.ProxyService
import com.hkspeedup.common.ui.InitMainUi
import com.hkspeedup.common.util.FileSelector
import com.hkspeedup.common.util.GlobalLog
import com.hkspeedup.common.util.InstallPermissionManager
import com.hkspeedup.common.viewmodel.ProxyStatus
import com.hkspeedup.common.viewmodel.ViewFunction


open class MainActivity : AppCompatActivity() {

    private fun setShortCur() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
            val shortcutManager = getSystemService(ShortcutManager::class.java)

            val enableProxyIntent = Intent(this, MainActivity::class.java)
                .apply {
                    putExtra("ENABLE_PROXY_FLAG", "true")
                    setAction(Intent.ACTION_MAIN)
                }
            val disableProxyIntent = Intent(this, MainActivity::class.java)
                .apply {
                    putExtra("ENABLE_PROXY_FLAG", "false")
                    setAction(Intent.ACTION_MAIN)
                }

            val enableProxyShortcut = ShortcutInfo.Builder(this, "enable_shortcut")
                .setShortLabel("Enable Proxy")
                .setLongLabel("Enable Proxy")
                .setIcon(Icon.createWithResource(this, R.drawable.baseline_toggle_on))
                .setIntent(enableProxyIntent)
                .build()

            val disableProxyShortcut = ShortcutInfo.Builder(this, "disable_shortcut")
                .setShortLabel("Disable Proxy")
                .setLongLabel("Disable Proxy")
                .setIcon(Icon.createWithResource(this, R.drawable.baseline_toggle_off))
                .setIntent(disableProxyIntent)
                .build()

            shortcutManager.dynamicShortcuts = listOf(disableProxyShortcut, enableProxyShortcut)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        GlobalLog.info("MainActivity onCreate")

        // 注册返回键的回调
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (!ViewFunction.backNavigationStackPage()) {
                    val intent = Intent(Intent.ACTION_MAIN)
                    intent.addCategory(Intent.CATEGORY_HOME)
                    startActivity(intent)
                }
            }
        })

        // 初始化文件选择器
        FileSelector.initialize(this)

        // 初始化安装权限管理器
        InstallPermissionManager.initialize(this)

        // 设置快捷方式
        setShortCur()

        ProxyService.registerForActivityResult = this.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) {
            ProxyService.activityResultCallback(it.resultCode)
        }

        initMain(arrayOf())

        ConfigService.setViewModel(MyApplication.viewModel)

        setContent {
            InitMainUi(MyApplication.viewModel)

            if (MyVpnService.VPN_ACTION == MyVpnService.Companion.VpnAction.START) {
                ViewFunction.setProxyStatus(ProxyStatus.START)
            }
        }
    }

    override fun onResume() {
        super.onResume()

        // 获取Intent传递的参数
        val enableProxyFlag = intent.getStringExtra("ENABLE_PROXY_FLAG")
        intent.removeExtra("ENABLE_PROXY_FLAG")
        enableProxyFlag
            ?.run { toString().toBooleanStrictOrNull() }
            ?.let {
                if (it) {
                    // 如果未启动状态则启动代理
                    if (MyApplication.viewModel.proxyStatus == ProxyStatus.STOP) {
                        ProxyService.startTunnel()
                    }
                } else {
                    // 停止代理
                    ProxyService.stopTunnel {}
                }
            }
    }
}
