import org.jetbrains.compose.desktop.application.dsl.TargetFormat
import java.time.Year

plugins {
    kotlin("multiplatform")
    kotlin("plugin.compose")
    id("org.jetbrains.compose")
}

group = "com.hkspeedup"
version = "1.0-SNAPSHOT"

kotlin {
    jvm {
        withJava()
    }
    sourceSets {
        val jvmMain by getting {
            dependencies {
                implementation(project(":common"))
                implementation(compose.desktop.currentOs)
            }
        }
        val jvmTest by getting
    }
}

compose.desktop {
    application {
        mainClass = "com.hkspeedup.desktop.MainKt"
        jvmArgs += listOf("-Xmx500M", "-XX:+HeapDumpOnOutOfMemoryError", "-XX:HeapDumpPath=~/.FlyShadow")

        nativeDistributions {
            // 包含jdk模块
            modules(
                "java.compiler",
                "java.instrument",
                "java.management",
                "java.naming",
                "java.scripting",
                "java.sql",
                "jdk.httpserver",
                "jdk.unsupported"
            )

            targetFormats(TargetFormat.Dmg, TargetFormat.Msi, TargetFormat.Deb)
            copyright = "Copyright © ${Year.now().value} FlyShadow. All rights reserved."
            vendor = "FlyShadow"
            packageName = "FlyShadow"
            packageVersion = findProperty("app.version") as String

            val iconsRoot = project.file("../common/src/commonMain/resources/mipmap")
            windows {
                menuGroup = "FlyShadow"
                dirChooser = true// 是否启用目录选择器功能
                menu = true // 是否启用应用程序的菜单功能
                shortcut = true // 是否创建应用程序的桌面快捷方式
                upgradeUuid = "61DAB35E-17CB-43B0-81D5-B30E1C0830FA"
                iconFile.set(iconsRoot.resolve("ic_launcher_round.ico"))
            }

            macOS {
                dockName = "FlyShadow"
                iconFile.set(iconsRoot.resolve("ic_launcher_round.icns"))
            }
            linux {
                iconFile.set(iconsRoot.resolve("ic_launcher_round.png"))
            }
        }
        buildTypes {
            release {
                proguard {
                    configurationFiles.from("../rules.pro")
                }
            }
        }
    }
}
