-ignorewarnings
-keep class cn.hutool.** { *; }
-keep class org.apache.logging.log4j.** { *; }
-keep class com.hkspeedup.common.lib.** { *; }
-keep class com.hkspeedup.common.vo.** { *; }
-keep class com.hkspeedup.common.service.FlyShadowApi { *; }
-keep class com.hkspeedup.common.service.Config { *; }
-keep class kotlinx.** { *; }
-keep class okhttp.** { *; }
-keep class okio.** { *; }
-keep class java.util.** { *; }
-keep class kotlin.coroutines.** { *; }

-keep class com.sun.jna.* { *; }

-keepclassmembers class * extends com.sun.jna.* { public *; }
-keepclassmembers class * extends kotlinx.* { public *; }
-keepclassmembers class * extends java.util.* { public *; }
-keepclassmembers class * extends kotlin.coroutines.* { public *; }

-keepclassmembers,allowoptimization enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keepnames class * implements java.io.Serializable

-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}