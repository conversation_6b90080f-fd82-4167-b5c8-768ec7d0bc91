plugins {
    kotlin("multiplatform").apply(false)
    kotlin("plugin.compose") apply false
    kotlin("android") apply false
    id("org.jetbrains.compose").apply(false)

    id("com.android.application").apply(false)
    id("com.android.library").apply(false)
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven("https://maven.pkg.jetbrains.space/public/p/compose/dev")
    }
}
